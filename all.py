import frappe
from frappe.utils import nowdate
from hrms.overrides.employee_payment_entry import get_payment_entry_for_employee
from contextlib import contextmanager


@contextmanager
def temporary_user(user):
    """Temporarily change session user"""
    if not frappe.db.exists("User", user):
        frappe.throw(f"User '{user}' does not exist")

    original_user = frappe.session.user
    frappe.session.user = user
    try:
        yield
    finally:
        frappe.session.user = original_user


def execute(doc, method):
    """Main execution function"""
    # Basic validation
    if doc.docstatus != 1 or not doc.travel_request_ref:
        return

    # Check if payment already exists
    if frappe.db.exists(
        "Payment Entry", {"reference_no": doc.name, "docstatus": ["!=", 2]}
    ):
        frappe.msgprint("Payment Entry already exists for this advance")
        return

    try:
        payment_entry = create_payment_entry(doc)

        if payment_entry:
            doc.reload()

    except Exception as e:
        frappe.throw(f"Error creating payment entry: {str(e)}")


def create_payment_entry(doc):
    """Create payment entry with validation"""
    # Validate required data
    if not doc.employee or not doc.advance_amount:
        frappe.throw("Employee and advance amount are required")

    if not frappe.db.exists("Employee", doc.employee):
        frappe.throw(f"Employee {doc.employee} does not exist")

    # Get payment entry with Administrator permissions
    with temporary_user("Administrator"):
        payment_entry = get_payment_entry_for_employee("Employee Advance", doc.name)

    if not payment_entry:
        frappe.throw("Could not generate payment entry")

    # Set reference details
    payment_entry.reference_no = doc.name
    payment_entry.reference_date = nowdate()

    # Set mandatory fields if missing
    if not payment_entry.get("payment_approval_group"):
        # Get first available approval group
        approval_groups = frappe.get_all("Payment Approval Group", limit=1)
        if approval_groups:
            payment_entry.payment_approval_group = approval_groups[0].name
        else:
            frappe.throw("No Payment Approval Group found. Please create one first.")

    # Bypass all permissions and validations
    payment_entry.flags.ignore_permissions = True
    payment_entry.flags.ignore_mandatory = True
    payment_entry.flags.ignore_validate = True
    payment_entry.flags.ignore_links = True
    payment_entry.flags.ignore_user_permissions = True
    payment_entry.insert(ignore_permissions=True)

    frappe.msgprint(f"Payment Entry {payment_entry.name} created successfully")
    return payment_entry
