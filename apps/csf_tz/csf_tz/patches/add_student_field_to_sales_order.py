import frappe
from frappe.custom.doctype.custom_field.custom_field import create_custom_fields


def execute():
    """Add student field to Sales Order to fix database column error"""
    
    # Check if the custom field already exists
    if frappe.db.exists("Custom Field", {"dt": "Sales Order", "fieldname": "student"}):
        print("Student field already exists in Sales Order")
        return
    
    # Define the custom field for Sales Order
    custom_fields = {
        "Sales Order": [
            {
                "fieldname": "student",
                "label": "Student",
                "fieldtype": "Link",
                "options": "Student",
                "insert_after": "customer_name",
                "fetch_from": "customer.student",
                "fetch_if_empty": 1,
                "read_only": 1,
                "translatable": 0,
            }
        ]
    }
    
    try:
        # Create the custom field
        create_custom_fields(custom_fields, update=True)
        print("Successfully added student field to Sales Order")
        
        # Reload the doctype to ensure the field is available
        frappe.reload_doctype("Sales Order")
        
    except Exception as e:
        print(f"Error adding student field to Sales Order: {str(e)}")
        frappe.log_error(f"Error adding student field to Sales Order: {str(e)}")
