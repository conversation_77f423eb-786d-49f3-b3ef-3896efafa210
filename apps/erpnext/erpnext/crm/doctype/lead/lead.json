{"actions": [], "allow_events_in_timeline": 1, "allow_import": 1, "allow_rename": 1, "autoname": "naming_series:", "creation": "2022-02-08 13:14:41.083327", "doctype": "DocType", "document_type": "Document", "email_append_to": 1, "engine": "InnoDB", "field_order": ["naming_series", "salutation", "first_name", "middle_name", "last_name", "column_break_1", "lead_name", "job_title", "gender", "source", "col_break123", "lead_owner", "status", "customer", "type", "request_type", "contact_info_tab", "email_id", "website", "column_break_20", "mobile_no", "whatsapp_no", "column_break_16", "phone", "phone_ext", "organization_section", "company_name", "no_of_employees", "column_break_28", "annual_revenue", "industry", "market_segment", "column_break_31", "territory", "fax", "address_section", "address_html", "column_break_38", "city", "state", "country", "column_break2", "contact_html", "qualification_tab", "qualification_status", "column_break_64", "qualified_by", "qualified_on", "other_info_tab", "campaign_name", "company", "column_break_22", "language", "image", "title", "column_break_50", "disabled", "unsubscribed", "blog_subscriber", "activities_tab", "open_activities_html", "all_activities_section", "all_activities_html", "notes_tab", "notes_html", "notes", "dashboard_tab"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "no_copy": 1, "oldfieldname": "naming_series", "oldfieldtype": "Select", "options": "CRM-LEAD-.YYYY.-", "set_only_once": 1}, {"depends_on": "eval:!doc.__islocal", "fieldname": "lead_name", "fieldtype": "Data", "in_global_search": 1, "label": "Full Name", "oldfieldname": "lead_name", "oldfieldtype": "Data", "read_only": 1, "search_index": 1}, {"fieldname": "company_name", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Organization Name", "mandatory_depends_on": "eval: !(doc.first_name)", "oldfieldname": "company_name", "oldfieldtype": "Data"}, {"fieldname": "email_id", "fieldtype": "Data", "label": "Email", "oldfieldname": "email_id", "oldfieldtype": "Data", "options": "Email", "search_index": 1}, {"fieldname": "col_break123", "fieldtype": "Column Break", "width": "50%"}, {"default": "__user", "fieldname": "lead_owner", "fieldtype": "Link", "label": "Lead Owner", "oldfieldname": "lead_owner", "oldfieldtype": "Link", "options": "User", "search_index": 1}, {"default": "Lead", "fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "in_standard_filter": 1, "label": "Status", "no_copy": 1, "oldfieldname": "status", "oldfieldtype": "Select", "options": "Lead\nOpen\nReplied\nOpportunity\nQuotation\nLost Quotation\nInterested\nConverted\nDo Not Contact", "reqd": 1, "search_index": 1}, {"fieldname": "salutation", "fieldtype": "Link", "label": "Salutation", "options": "Salutation"}, {"fieldname": "gender", "fieldtype": "Link", "label": "Gender", "options": "Gender"}, {"fieldname": "source", "fieldtype": "Link", "label": "Source", "oldfieldname": "source", "oldfieldtype": "Select", "options": "Lead Source"}, {"depends_on": "eval:doc.source == 'Existing Customer'", "fieldname": "customer", "fieldtype": "Link", "label": "From Customer", "no_copy": 1, "oldfieldname": "customer", "oldfieldtype": "Link", "options": "Customer"}, {"fieldname": "campaign_name", "fieldtype": "Link", "label": "Campaign Name", "oldfieldname": "campaign_name", "oldfieldtype": "Link", "options": "Campaign"}, {"fieldname": "image", "fieldtype": "Attach Image", "hidden": 1, "label": "Image", "print_hide": 1}, {"fieldname": "contact_info_tab", "fieldtype": "Section Break", "label": "Contact Info"}, {"fieldname": "address_html", "fieldtype": "HTML", "label": "Address HTML", "read_only": 1}, {"fieldname": "column_break2", "fieldtype": "Column Break"}, {"fieldname": "contact_html", "fieldtype": "HTML", "label": "Contact HTML", "read_only": 1}, {"fieldname": "phone", "fieldtype": "Data", "label": "Phone", "oldfieldname": "contact_no", "oldfieldtype": "Data", "options": "Phone"}, {"fieldname": "mobile_no", "fieldtype": "Data", "label": "Mobile No", "oldfieldname": "mobile_no", "oldfieldtype": "Data", "options": "Phone"}, {"fieldname": "fax", "fieldtype": "Data", "label": "Fax", "oldfieldname": "fax", "oldfieldtype": "Data"}, {"fieldname": "type", "fieldtype": "Select", "label": "Lead Type", "oldfieldname": "type", "oldfieldtype": "Select", "options": "\nClient\nChannel Partner\nConsultant"}, {"fieldname": "market_segment", "fieldtype": "Link", "label": "Market Segment", "oldfieldname": "market_segment", "oldfieldtype": "Select", "options": "Market Segment"}, {"fieldname": "industry", "fieldtype": "Link", "label": "Industry", "oldfieldname": "industry", "oldfieldtype": "Link", "options": "Industry Type"}, {"fieldname": "request_type", "fieldtype": "Select", "label": "Request Type", "oldfieldname": "request_type", "oldfieldtype": "Select", "options": "\nProduct Enquiry\nRequest for Information\nSuggestions\nOther"}, {"fieldname": "company", "fieldtype": "Link", "label": "Company", "oldfieldname": "company", "oldfieldtype": "Link", "options": "Company", "remember_last_selected_value": 1}, {"fieldname": "website", "fieldtype": "Data", "label": "Website", "oldfieldname": "website", "oldfieldtype": "Data"}, {"fieldname": "territory", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Territory", "oldfieldname": "territory", "oldfieldtype": "Link", "options": "Territory", "print_hide": 1}, {"default": "0", "fieldname": "unsubscribed", "fieldtype": "Check", "label": "Unsubscribed"}, {"default": "0", "fieldname": "blog_subscriber", "fieldtype": "Check", "label": "Blog Subscriber"}, {"fieldname": "title", "fieldtype": "Data", "hidden": 1, "label": "Title", "print_hide": 1, "read_only": 1}, {"fieldname": "language", "fieldtype": "Link", "label": "Print Language", "options": "Language"}, {"fieldname": "first_name", "fieldtype": "Data", "label": "First Name", "mandatory_depends_on": "eval: !(doc.company_name)"}, {"fieldname": "middle_name", "fieldtype": "Data", "label": "Middle Name"}, {"fieldname": "last_name", "fieldtype": "Data", "label": "Last Name"}, {"fieldname": "no_of_employees", "fieldtype": "Select", "label": "No of Employees", "options": "1-10\n11-50\n51-200\n201-500\n501-1000\n1000+"}, {"fieldname": "column_break_22", "fieldtype": "Column Break"}, {"fieldname": "whatsapp_no", "fieldtype": "Data", "label": "WhatsApp", "options": "Phone"}, {"fieldname": "column_break_50", "fieldtype": "Column Break"}, {"fieldname": "column_break_16", "fieldtype": "Column Break"}, {"fieldname": "phone_ext", "fieldtype": "Data", "label": "Phone Ext."}, {"collapsible": 1, "fieldname": "qualification_tab", "fieldtype": "Section Break", "label": "Qualification"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "notes_tab", "fieldtype": "Tab Break", "label": "Notes"}, {"collapsible": 1, "fieldname": "other_info_tab", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "column_break_1", "fieldtype": "Column Break"}, {"fieldname": "qualified_by", "fieldtype": "Link", "label": "Qualified By", "options": "User"}, {"fieldname": "qualified_on", "fieldtype": "Date", "label": "Qualified on"}, {"fieldname": "qualification_status", "fieldtype": "Select", "label": "Qualification Status", "options": "Unqualified\nIn Process\nQualified"}, {"collapsible": 1, "fieldname": "address_section", "fieldtype": "Section Break", "label": "Address & Contacts"}, {"fieldname": "column_break_64", "fieldtype": "Column Break"}, {"fieldname": "column_break_20", "fieldtype": "Column Break"}, {"fieldname": "job_title", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Job Title"}, {"fieldname": "annual_revenue", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Annual Revenue"}, {"depends_on": "eval:!doc.__islocal", "fieldname": "activities_tab", "fieldtype": "Tab Break", "label": "Activities"}, {"fieldname": "organization_section", "fieldtype": "Section Break", "label": "Organization"}, {"fieldname": "column_break_28", "fieldtype": "Column Break"}, {"fieldname": "column_break_31", "fieldtype": "Column Break"}, {"fieldname": "notes_html", "fieldtype": "HTML", "label": "Notes HTML"}, {"fieldname": "open_activities_html", "fieldtype": "HTML", "label": "Open Activities HTML"}, {"fieldname": "all_activities_section", "fieldtype": "Section Break", "label": "All Activities"}, {"fieldname": "all_activities_html", "fieldtype": "HTML", "label": "All Activities HTML"}, {"fieldname": "notes", "fieldtype": "Table", "hidden": 1, "label": "Notes", "no_copy": 1, "options": "CRM Note"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "column_break_38", "fieldtype": "Column Break"}, {"fieldname": "city", "fieldtype": "Data", "label": "City"}, {"fieldname": "state", "fieldtype": "Data", "label": "State/Province"}, {"fieldname": "country", "fieldtype": "Link", "label": "Country", "options": "Country"}, {"fieldname": "dashboard_tab", "fieldtype": "Tab Break", "label": "Connections", "show_dashboard": 1}], "grid_page_length": 50, "icon": "fa fa-user", "idx": 5, "image_field": "image", "links": [], "modified": "2025-06-26 11:02:01.158901", "modified_by": "Administrator", "module": "CRM", "name": "Lead", "naming_rule": "By \"Naming Series\" field", "owner": "Administrator", "permissions": [{"permlevel": 1, "read": 1, "report": 1, "role": "Desk User"}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "Sales User", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "import": 1, "print": 1, "read": 1, "report": 1, "role": "Sales Manager", "share": 1, "write": 1}, {"create": 1, "email": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"permlevel": 1, "read": 1, "report": 1, "role": "Sales Manager"}, {"permlevel": 1, "read": 1, "report": 1, "role": "Sales User"}], "row_format": "Dynamic", "search_fields": "lead_name,lead_owner,status", "sender_field": "email_id", "sender_name_field": "lead_name", "show_name_in_global_search": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "subject_field": "title", "title_field": "title"}