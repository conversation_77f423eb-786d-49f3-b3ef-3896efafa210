2025-06-02 11:33:11,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Details` DROP INDEX `modified`
2025-06-02 11:33:11,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabTire Checklist` DROP INDEX `modified`
2025-06-02 11:33:12,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Checklist` DROP INDEX `modified`
2025-06-02 11:33:12,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel System Details` DROP INDEX `modified`
2025-06-02 11:33:12,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist Details` DROP INDEX `modified`
2025-06-02 11:33:12,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Details` DROP INDEX `modified`
2025-06-02 11:33:12,185 WARNING database DDL Query made to DB:
<PERSON>TER TABLE `tabBrake System Details` DROP INDEX `modified`
2025-06-02 11:33:12,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Checklist` DROP INDEX `modified`
2025-06-02 11:33:12,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabSession Default` DROP INDEX `modified`
2025-06-02 11:33:12,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` DROP INDEX `modified`
2025-06-02 11:33:12,316 WARNING database DDL Query made to DB:
ALTER TABLE `tabCampaign Email Schedule` DROP INDEX `modified`
2025-06-02 11:33:12,347 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Medium Timeslot` DROP INDEX `modified`
2025-06-02 11:33:12,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Meeting Minutes` DROP INDEX `modified`
2025-06-02 11:33:12,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Feedback Template Parameter` DROP INDEX `modified`
2025-06-02 11:33:12,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Meeting Agenda` DROP INDEX `modified`
2025-06-02 11:33:12,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabCommunication Link` DROP INDEX `modified`
2025-06-02 11:33:12,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan NFS Repayments` DROP INDEX `modified`
2025-06-02 11:33:12,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Item Code` DROP INDEX `modified`
2025-06-02 11:33:12,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Brand` DROP INDEX `modified`
2025-06-02 11:33:12,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Item Group` DROP INDEX `modified`
2025-06-02 11:33:12,632 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Detail` DROP INDEX `modified`
2025-06-02 11:33:12,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabPricing Rule Detail` DROP INDEX `modified`
2025-06-02 11:33:12,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabCashier Closing Payments` DROP INDEX `modified`
2025-06-02 11:33:12,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Components` DROP INDEX `modified`
2025-06-02 11:33:12,793 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Overtime` DROP INDEX `modified`
2025-06-02 11:33:12,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Components` DROP INDEX `modified`
2025-06-02 11:33:12,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabHomepage Section Card` DROP INDEX `modified`
2025-06-02 11:33:12,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Criteria` DROP INDEX `modified`
2025-06-02 11:33:12,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Variable` DROP INDEX `modified`
2025-06-02 11:33:12,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Scorecard Scoring Standing` DROP INDEX `modified`
2025-06-02 11:33:13,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` DROP INDEX `modified`
2025-06-02 11:33:13,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Detail` DROP INDEX `modified`
2025-06-02 11:33:13,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabLost Reason Detail` DROP INDEX `modified`
2025-06-02 11:33:13,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax Template Detail` DROP INDEX `modified`
2025-06-02 11:33:13,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabPractitioner Service Unit Schedule` DROP INDEX `modified`
2025-06-02 11:33:13,166 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Mapping` DROP INDEX `modified`
2025-06-02 11:33:13,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrder Tracking Container` DROP INDEX `modified`
2025-06-02 11:33:13,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Reading` DROP INDEX `modified`
2025-06-02 11:33:13,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Details` DROP INDEX `modified`
2025-06-02 11:33:13,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` DROP INDEX `modified`
2025-06-02 11:33:13,336 WARNING database DDL Query made to DB:
ALTER TABLE `tabLinked Location` DROP INDEX `modified`
2025-06-02 11:33:13,370 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` DROP INDEX `modified`
2025-06-02 11:33:13,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` DROP INDEX `modified`
2025-06-02 11:33:13,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupport Search Source` DROP INDEX `modified`
2025-06-02 11:33:13,478 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Team Member` DROP INDEX `modified`
2025-06-02 11:33:13,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Category Account` DROP INDEX `modified`
2025-06-02 11:33:13,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Fulfilment Checklist` DROP INDEX `modified`
2025-06-02 11:33:13,577 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract Template Fulfilment Terms` DROP INDEX `modified`
2025-06-02 11:33:13,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabClosed Document` DROP INDEX `modified`
2025-06-02 11:33:13,647 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Point Entry Redemption` DROP INDEX `modified`
2025-06-02 11:33:13,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Customer Detail` DROP INDEX `modified`
2025-06-02 11:33:13,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` DROP INDEX `modified`
2025-06-02 11:33:13,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment Account` DROP INDEX `modified`
2025-06-02 11:33:13,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip Location Update` DROP INDEX `modified`
2025-06-02 11:33:13,895 WARNING database DDL Query made to DB:
ALTER TABLE `tabCash Flow Mapping Accounts` DROP INDEX `modified`
2025-06-02 11:33:13,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer File Closing Information` DROP INDEX `modified`
2025-06-02 11:33:13,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Status Table` DROP INDEX `modified`
2025-06-02 11:33:14,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoyalty Program Collection` DROP INDEX `modified`
2025-06-02 11:33:14,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabShare Balance` DROP INDEX `modified`
2025-06-02 11:33:14,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Material Request` DROP INDEX `modified`
2025-06-02 11:33:14,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sales Order` DROP INDEX `modified`
2025-06-02 11:33:14,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabSensitivity Test Result` DROP INDEX `modified`
2025-06-02 11:33:14,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` DROP INDEX `modified`
2025-06-02 11:33:14,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` DROP INDEX `modified`
2025-06-02 11:33:14,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabDosage Strength` DROP INDEX `modified`
2025-06-02 11:33:14,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` DROP INDEX `modified`
2025-06-02 11:33:14,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` DROP INDEX `modified`
2025-06-02 11:33:14,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` DROP INDEX `modified`
2025-06-02 11:33:14,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond History Table` DROP INDEX `modified`
2025-06-02 11:33:14,462 WARNING database DDL Query made to DB:
ALTER TABLE `tabMandatory Attachment Table` DROP INDEX `modified`
2025-06-02 11:33:14,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Documents` DROP INDEX `modified`
2025-06-02 11:33:14,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabPermits Table` DROP INDEX `modified`
2025-06-02 11:33:14,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport Border Procedure Table` DROP INDEX `modified`
2025-06-02 11:33:14,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Procedure Table` DROP INDEX `modified`
2025-06-02 11:33:14,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubtrips Table` DROP INDEX `modified`
2025-06-02 11:33:14,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabEquipment Table` DROP INDEX `modified`
2025-06-02 11:33:14,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequired Permit` DROP INDEX `modified`
2025-06-02 11:33:14,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Operation` DROP INDEX `modified`
2025-06-02 11:33:14,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Website Item` DROP INDEX `modified`
2025-06-02 11:33:14,833 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Item Group` DROP INDEX `modified`
2025-06-02 11:33:14,865 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Customer Group` DROP INDEX `modified`
2025-06-02 11:33:14,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Item Group` DROP INDEX `modified`
2025-06-02 11:33:14,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabUOM Conversion Detail` DROP INDEX `modified`
2025-06-02 11:33:14,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Country` DROP INDEX `modified`
2025-06-02 11:33:14,998 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Rule Condition` DROP INDEX `modified`
2025-06-02 11:33:15,032 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice List Country` DROP INDEX `modified`
2025-06-02 11:33:15,066 WARNING database DDL Query made to DB:
ALTER TABLE `tabMonthly Distribution Percentage` DROP INDEX `modified`
2025-06-02 11:33:15,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Website Specification` DROP INDEX `modified`
2025-06-02 11:33:15,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant` DROP INDEX `modified`
2025-06-02 11:33:15,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Attribute Value` DROP INDEX `modified`
2025-06-02 11:33:15,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Internal Work History` DROP INDEX `modified`
2025-06-02 11:33:15,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee External Work History` DROP INDEX `modified`
2025-06-02 11:33:15,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Education` DROP INDEX `modified`
2025-06-02 11:33:16,612 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-06-02 11:33:16,805 WARNING database DDL Query made to DB:
truncate `tabAccount Closing Balance`
2025-06-02 11:33:18,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `healthcare_practitioner` varchar(140)
2025-06-02 11:33:18,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-02 11:33:18,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` ADD COLUMN `healthcare_service_unit` varchar(140)
2025-06-02 11:33:18,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Request` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-06-02 11:33:19,034 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-06-02 11:33:19,284 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `posting_datetime_creation_index`
2025-06-02 11:33:19,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `posting_datetime_creation_index`(posting_datetime, creation)
2025-06-02 11:33:20,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:33:20,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `healthcare_practitioner` varchar(140)
2025-06-02 11:33:20,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0
2025-06-02 11:33:20,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `healthcare_service_unit` varchar(140)
2025-06-02 11:33:21,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-02 11:33:21,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0
2025-06-02 11:33:21,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0
2025-06-02 11:33:21,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_control_account` varchar(140)
2025-06-02 11:33:23,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0
2025-06-02 11:33:23,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `custom_is_trade_in` int(1) not null default 0
2025-06-02 11:33:23,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `custom_uom` varchar(140), ADD COLUMN `custom_trade_in_item` varchar(140), ADD COLUMN `custom_trade_in_qty` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_uom` varchar(140), ADD COLUMN `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, ADD COLUMN `custom_total_trade_in_value` decimal(21,9) not null default 0, ADD COLUMN `custom_trade_in_batch_no` varchar(140), ADD COLUMN `custom_trade_in_serial_no` text
2025-06-02 11:33:23,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0
2025-06-02 11:33:23,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `custom_sales_invoice` varchar(140)
2025-06-02 11:33:23,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `repack_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0
2025-06-02 11:33:26,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `last_transaction_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:33:28,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-06-02 11:33:28,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-06-02 11:33:29,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-02 11:33:29,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-06-02 11:33:29,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,628 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-06-02 11:33:29,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0
2025-06-02 11:33:29,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-06-02 11:33:29,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-06-02 11:33:29,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-06-02 11:33:30,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0
2025-06-02 11:33:30,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-06-02 11:33:30,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0
2025-06-02 11:33:30,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-06-02 11:33:31,079 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` ADD COLUMN `admission_type` varchar(140)
2025-06-02 11:33:31,105 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:33:31,457 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:31,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:31,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text
2025-06-02 11:33:31,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 11:33:32,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` ADD COLUMN `ward_type` varchar(140)
2025-06-02 11:33:32,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:33:32,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:32,413 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0
2025-06-02 11:33:32,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` ADD COLUMN `approval_date` date
2025-06-02 11:33:33,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure` MODIFY `consumable_total_amount` decimal(21,9) not null default 0
2025-06-02 11:33:33,548 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` ADD COLUMN `approval_date` date, ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:33,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `returned_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `installed_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `last_qty_prescribed` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `original_stock_uom_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `billed_amt` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `recommended_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-06-02 11:33:33,839 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD COLUMN `insurance_provider` varchar(140), ADD COLUMN `national_id` varchar(140) unique
2025-06-02 11:33:33,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient` ADD UNIQUE INDEX IF NOT EXISTS national_id (`national_id`)
2025-06-02 11:33:34,200 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:34,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:33:34,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `jubilee_patient_claim` varchar(140), ADD COLUMN `years_of_insurance` int(11) not null default 0, ADD COLUMN `require_fingerprint` int(1) not null default 0, ADD COLUMN `require_facial_recognation` int(1) not null default 0, ADD COLUMN `biometric_method` varchar(140), ADD COLUMN `fpcode` varchar(140)
2025-06-02 11:33:35,000 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 11:33:35,367 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `is_restricted` int(1) not null default 0, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:35,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:33:35,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` ADD COLUMN `shm_organogram_department` varchar(140), ADD COLUMN `has_preapproval` int(1) not null default 0
2025-06-02 11:33:35,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 11:33:36,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` ADD COLUMN `workflow_state` varchar(140), ADD COLUMN `approval_status` varchar(140), ADD COLUMN `approval_date` date, ADD COLUMN `authorized_item_id` varchar(140), ADD COLUMN `service_authorization_id` varchar(140)
2025-06-02 11:33:36,155 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:33:36,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Examination` ADD COLUMN `approval_date` date
2025-06-02 11:33:36,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Code` ADD COLUMN `is_non_specific` int(1) not null default 0
2025-06-02 11:33:36,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan` ADD COLUMN `prescribe` int(1) not null default 0
2025-06-02 11:33:37,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD COLUMN `room_type` varchar(140), ADD COLUMN `is_consultancy_chargeable` int(1) not null default 0, ADD COLUMN `is_service_chargeable` int(1) not null default 0
2025-06-02 11:33:37,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type` ADD COLUMN `disabled` int(1) not null default 0, ADD COLUMN `required_input` varchar(140), ADD COLUMN `visit_type_name_alias` varchar(140), ADD COLUMN `maximum_visit_per_month` varchar(140), ADD COLUMN `requires_remarks` int(1) not null default 0, ADD COLUMN `requires_referral_no` int(1) not null default 0, ADD COLUMN `description` text, ADD COLUMN `has_followup_charges` int(1) not null default 0, ADD COLUMN `has_fasttrack_charges` int(1) not null default 0
2025-06-02 11:33:37,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` ADD COLUMN `preapproval_status` varchar(140), ADD COLUMN `preapproval_no` varchar(140), ADD COLUMN `rejection_reason_code` varchar(140), ADD COLUMN `rejection_details` text, ADD COLUMN `preapproval_cancel_remarks` text, ADD COLUMN `has_copayment` int(1) not null default 0
2025-06-02 11:33:37,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:33:38,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` ADD COLUMN `national_id` varchar(140), ADD COLUMN `date_loggedin_to_nhif` date, ADD COLUMN `shm_doctor_organogram_department` varchar(140)
2025-06-02 11:33:38,173 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `op_consulting_charge` decimal(21,9) not null default 0, MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS practitioner_name (`practitioner_name`)
2025-06-02 11:33:38,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `service_request` varchar(140), ADD COLUMN `vehicle` varchar(140), ADD COLUMN `shmh_organogram_department` varchar(140)
2025-06-02 11:33:38,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-02 11:33:38,990 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test` ADD COLUMN `approval_date` date
2025-06-02 11:33:39,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` ADD COLUMN `national_id` varchar(140), ADD COLUMN `verifier_id` varchar(140), ADD COLUMN `card_type_id` varchar(140), ADD COLUMN `card_type_name` varchar(140)
2025-06-02 11:33:39,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Subscription` MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-03 12:53:58,658 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 12:53:59,690 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 12:54:01,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 12:54:02,822 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 12:54:03,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 12:54:03,871 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 12:54:04,102 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 12:54:04,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 12:54:05,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 12:54:06,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `create_cash_journal` int(1) not null default 0, ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0
2025-06-03 12:54:06,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-06-03 12:54:06,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `pension_fund` varchar(140), ADD COLUMN `pension_fund_number` varchar(140), ADD COLUMN `wcf_number` varchar(140), ADD COLUMN `tin_number` varchar(140), ADD COLUMN `national_identity` varchar(140), ADD COLUMN `bank_code` varchar(140)
2025-06-03 12:54:06,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-06-03 12:54:06,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0
2025-06-03 12:54:12,596 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-03 12:54:12,621 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-03 12:54:12,912 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-03 12:54:12,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `custom_trade_in_sales_percentage` decimal(21,9) not null default 0, MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-06-03 12:54:13,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `restrict_unallocated_amount_for_supplier` int(1) not null default 0
2025-06-03 12:54:13,028 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `naming_series` varchar(140) default 'RE-.YYYY.-', MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-03 15:20:29,658 WARNING database DDL Query made to DB:
DROP USER IF EXISTS '_735b0b6f43cd3001'@'localhost'
2025-06-03 15:20:48,503 WARNING database DDL Query made to DB:
DROP DATABASE IF EXISTS `_735b0b6f43cd3001`
2025-06-03 15:20:48,508 WARNING database DDL Query made to DB:
CREATE USER '_735b0b6f43cd3001'@'localhost' IDENTIFIED BY 'PiooDemKkYUVnx5o'
2025-06-03 15:20:48,517 WARNING database DDL Query made to DB:
CREATE DATABASE `_735b0b6f43cd3001` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
2025-06-03 15:27:46,635 WARNING database DDL Query made to DB:
create table if not exists `__Auth` (
				`doctype` VARCHAR(140) NOT NULL,
				`name` VARCHAR(255) NOT NULL,
				`fieldname` VARCHAR(140) NOT NULL,
				`password` TEXT NOT NULL,
				`encrypted` INT(1) NOT NULL DEFAULT 0,
				PRIMARY KEY (`doctype`, `name`, `fieldname`)
			) ENGINE=InnoDB ROW_FORMAT=DYNAMIC CHARACTER SET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-03 15:27:46,645 WARNING database DDL Query made to DB:
create table if not exists __UserSettings (
			`user` VARCHAR(180) NOT NULL,
			`doctype` VARCHAR(180) NOT NULL,
			`data` TEXT,
			UNIQUE(user, doctype)
			) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
2025-06-03 17:50:16,770 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:50:18,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:50:20,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:50:23,133 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:50:23,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:50:25,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:50:25,849 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 17:54:53,180 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:54:54,850 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:54:56,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:54:59,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:54:59,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:55:01,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-03 17:55:01,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:57:02,634 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-03 17:57:04,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-03 17:57:06,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-03 17:57:08,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:57:08,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-03 17:57:10,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-03 17:57:10,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-03 17:57:17,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0
2025-06-03 17:57:18,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0
2025-06-04 22:44:50,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD COLUMN `goal_no` varchar(140) unique
2025-06-04 22:44:50,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD UNIQUE INDEX IF NOT EXISTS goal_no (`goal_no`)
2025-06-04 22:49:14,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Target` ADD COLUMN `goal_no` varchar(140), ADD COLUMN `target_no` varchar(140) unique
2025-06-04 22:49:14,740 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Target` ADD UNIQUE INDEX IF NOT EXISTS target_no (`target_no`)
2025-06-04 22:50:53,579 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD COLUMN `target_no` varchar(140), ADD COLUMN `indicator_no` varchar(140) unique
2025-06-04 22:50:53,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD UNIQUE INDEX IF NOT EXISTS indicator_no (`indicator_no`)
2025-06-04 22:51:43,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Indicator` ADD COLUMN `description` text
2025-06-04 22:54:37,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabESG Criterion` ADD COLUMN `factor` varchar(140), ADD COLUMN `criteria` varchar(140)
2025-06-04 22:59:34,140 WARNING database DDL Query made to DB:
ALTER TABLE `tabESG Criterion` ADD UNIQUE INDEX IF NOT EXISTS criteria (`criteria`)
2025-06-05 12:03:10,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD COLUMN `amended_from` varchar(140)
2025-06-05 12:03:11,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabSDG Goal` ADD INDEX `amended_from_index`(`amended_from`)
2025-06-12 08:32:16,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` MODIFY `estimated_cost` decimal(21,9) not null default 0
2025-06-12 08:32:17,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:17,164 WARNING database DDL Query made to DB:
ALTER TABLE `tabSection` MODIFY `monthly_target` decimal(21,9) not null default 0
2025-06-12 08:32:17,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` MODIFY `old_price` decimal(21,9) not null default 0, MODIFY `cost` decimal(21,9) not null default 0, MODIFY `new_price` decimal(21,9) not null default 0
2025-06-12 08:32:17,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` MODIFY `vat` decimal(21,9) not null default 0, MODIFY `amt_ex__sr` decimal(21,9) not null default 0, MODIFY `amt_excl_vat` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-12 08:32:18,260 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report` MODIFY `a_vat` decimal(21,9) not null default 0, MODIFY `c_vat` decimal(21,9) not null default 0, MODIFY `d_turnover` decimal(21,9) not null default 0, MODIFY `a_net_sum` decimal(21,9) not null default 0, MODIFY `a_turnover` decimal(21,9) not null default 0, MODIFY `allowable_difference` decimal(21,9) not null default 0, MODIFY `total_turnover_ticked` decimal(21,9) not null default 0, MODIFY `e_turnover` decimal(21,9) not null default 0, MODIFY `money` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `total_vat` decimal(21,9) not null default 0, MODIFY `d_vat` decimal(21,9) not null default 0, MODIFY `total_excluding_vat_ticked` decimal(21,9) not null default 0, MODIFY `d_net_sum` decimal(21,9) not null default 0, MODIFY `b_turnover` decimal(21,9) not null default 0, MODIFY `total_turnover_ex_sr` decimal(21,9) not null default 0, MODIFY `c_net_sum` decimal(21,9) not null default 0, MODIFY `total_turnover` decimal(21,9) not null default 0, MODIFY `total_vat_ticked` decimal(21,9) not null default 0, MODIFY `b_vat` decimal(21,9) not null default 0, MODIFY `b_net_sum` decimal(21,9) not null default 0, MODIFY `total_turnover_exempted__sp_relief_ticked` decimal(21,9) not null default 0, MODIFY `c_turnover` decimal(21,9) not null default 0
2025-06-12 08:32:18,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:18,651 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:19,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-12 08:32:20,624 WARNING database DDL Query made to DB:
ALTER TABLE `tabNMB Callback` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:20,947 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting GL Entry` MODIFY `credit_amount` decimal(21,9) not null default 0, MODIFY `debit_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 08:32:21,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-12 08:32:21,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` MODIFY `cost_per_unit` decimal(21,9) not null default 0
2025-06-12 08:32:21,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` MODIFY `item_balance` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:21,857 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Cover Note` MODIFY `exchangerate` decimal(21,9) not null default 0, MODIFY `totalpremiumamountincludingtax` decimal(21,9) not null default 0, MODIFY `totalpremiumamountexcludingtax` decimal(21,9) not null default 0, MODIFY `commisionpaid` decimal(21,9) not null default 0, MODIFY `commisionrate` decimal(21,9) not null default 0
2025-06-12 08:32:22,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:32:23,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` MODIFY `flt_amount` decimal(21,9) not null default 0
2025-06-12 08:32:23,316 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`type` varchar(140) default 'Sales',
`verification_code` varchar(140) unique,
`verification_status` varchar(140) default 'Pending',
`verification_date` date,
`company_name` varchar(140),
`receipt_number` varchar(140),
`subtotal` decimal(21,9) not null default 0,
`total_tax` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 08:32:23,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0
2025-06-12 08:32:23,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-06-12 08:32:23,877 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:24,317 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0
2025-06-12 08:32:24,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:24,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` MODIFY `invoice_exchange_rate` decimal(21,9) not null default 0, MODIFY `invoice_amount` decimal(21,9) not null default 0, MODIFY `invoice_gain_or_loss` decimal(21,9) not null default 0
2025-06-12 08:32:25,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill` MODIFY `billequivalentamount` decimal(21,9) not null default 0, MODIFY `miscellaneousamount` decimal(21,9) not null default 0, MODIFY `billedamount` decimal(21,9) not null default 0
2025-06-12 08:32:26,183 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Material Request Details` MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:26,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` MODIFY `task_rate` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:26,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` MODIFY `billitemamount` decimal(21,9) not null default 0, MODIFY `billitemmiscamount` decimal(21,9) not null default 0, MODIFY `billitemeqvamount` decimal(21,9) not null default 0
2025-06-12 08:32:27,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` MODIFY `limit_amount` decimal(21,9) not null default 0
2025-06-12 08:32:27,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` MODIFY `debit_amount` decimal(21,9) not null default 0
2025-06-12 08:32:27,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-12 08:32:28,007 WARNING database DDL Query made to DB:
create table `tabTRA TAX Inv Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`description` varchar(140),
`quantity` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-12 08:32:28,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Record` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:28,764 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpen Invoice Exchange Rate Revaluation` MODIFY `exchange_rate_to_company_currency` decimal(21,9) not null default 0
2025-06-12 08:32:31,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:32:31,568 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection` MODIFY `sample_size` decimal(21,9) not null default 0
2025-06-12 08:32:31,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` MODIFY `request_amount` decimal(21,9) not null default 0
2025-06-12 08:32:32,111 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Accounts Table` MODIFY `request_amount` decimal(21,9) not null default 0, MODIFY `payable_account_currency` varchar(140)
2025-06-12 08:32:32,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabPre Delivery Inspection Old` MODIFY `customer_signature` longtext default 'Customer Signature'
2025-06-12 08:32:32,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabReference Payment Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:32,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond Reference Table` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-12 08:32:33,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:33,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:33,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer` DROP INDEX `net_weight_index`, DROP INDEX `container_no_index`, DROP INDEX `gross_weight_index`
2025-06-12 08:32:34,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpenses` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-12 08:32:34,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabImport File` MODIFY `invoice_amount` decimal(21,9) not null default 0
2025-06-12 08:32:35,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` MODIFY `net_weight` decimal(21,9) not null default 0, MODIFY `bond_value` decimal(21,9) not null default 0, MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `tare_weight` decimal(21,9) not null default 0
2025-06-12 08:32:36,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Clearance` MODIFY `goods_quantity` decimal(21,9) not null default 0, MODIFY `loose_net_weight` decimal(21,9) not null default 0, MODIFY `loose_gross_weight` decimal(21,9) not null default 0
2025-06-12 08:32:36,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabBond` MODIFY `bond_value` decimal(21,9) not null default 0
2025-06-12 08:32:36,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking List` MODIFY `gross_weight` decimal(21,9) not null default 0, MODIFY `net_weight` decimal(21,9) not null default 0
2025-06-12 08:32:38,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Request` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:38,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-06-12 08:32:39,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `cost_per_litre` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:39,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Steps Table` MODIFY `distance` decimal(21,9) not null default 0
2025-06-12 08:32:40,436 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-06-12 08:32:41,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense` MODIFY `fixed_value` decimal(21,9) not null default 0
2025-06-12 08:32:41,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,223 WARNING database DDL Query made to DB:
ALTER TABLE `tabFixed Expense Table` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Trip` MODIFY `main_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0
2025-06-12 08:32:42,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabTrip Route` MODIFY `total_tzs` decimal(21,9) not null default 0, MODIFY `total_usd` decimal(21,9) not null default 0
2025-06-12 08:32:42,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:44,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabProducts of Interest` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-06-12 08:32:44,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Sales` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Plan` MODIFY `planned_amount` decimal(21,9) not null default 0, MODIFY `actual_amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,280 WARNING database DDL Query made to DB:
ALTER TABLE `tabPast Serial No` MODIFY `amount` decimal(21,9) not null default 0
2025-06-12 08:32:45,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabIssued Items Table` MODIFY `requested` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `issued` decimal(21,9) not null default 0
2025-06-12 08:32:45,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabUsed Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:46,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Items Table` MODIFY `quantity` decimal(21,9) not null default 0
2025-06-12 08:32:46,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkshop Services Table` MODIFY `rate_per_hour` decimal(21,9) not null default 0, MODIFY `billable_hours` decimal(21,9) not null default 0
2025-06-12 08:32:47,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` MODIFY `transfer_amount` decimal(21,9) not null default 0
2025-06-12 08:32:47,719 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Initiation` MODIFY `control_sum` decimal(21,9) not null default 0
2025-06-12 08:32:48,472 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` ADD COLUMN `custom_supplier_name` varchar(140)
2025-06-12 08:32:48,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 08:33:30,331 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-12 08:33:31,805 WARNING database DDL Query made to DB:
ALTER TABLE `tabMode of Payment` ADD COLUMN `vfd_payment_type` varchar(140)
2025-06-12 08:33:31,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0
2025-06-12 08:33:32,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-06-12 08:33:32,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-06-12 08:33:33,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-06-12 08:33:34,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabContract` ADD COLUMN `party_full_name` varchar(140)
2025-06-12 08:33:34,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:35,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:35,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation` MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0
2025-06-12 08:33:36,395 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:36,417 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0
2025-06-12 08:33:37,386 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:37,412 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0
2025-06-12 08:33:38,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` ADD COLUMN `has_unit_price_items` int(1) not null default 0
2025-06-12 08:33:38,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation` MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0
2025-06-12 08:33:38,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-06-12 08:33:38,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `other_allowance` decimal(21,9) not null default 0, MODIFY `worker_subsistence` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0
2025-06-12 08:33:39,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` ADD COLUMN `ordered_qty` decimal(21,9) not null default 0
2025-06-12 08:33:39,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` MODIFY `wo_produced_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-06-12 08:33:39,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_value_difference` decimal(21,9) not null default 0, MODIFY `qty_after_transaction` decimal(21,9) not null default 0, MODIFY `stock_value` decimal(21,9) not null default 0, MODIFY `outgoing_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0
2025-06-12 08:33:39,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry` DROP INDEX `item_code`, DROP INDEX `warehouse`, DROP INDEX `posting_date_index`
2025-06-12 08:33:39,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Ledger Entry`
				ADD INDEX IF NOT EXISTS `item_code_warehouse_posting_datetime_creation_index`(item_code, warehouse, posting_datetime, creation)
2025-06-12 08:33:40,194 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0
2025-06-12 08:33:40,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` ADD COLUMN `expense_account` varchar(140), ADD COLUMN `cost_center` varchar(140)
2025-06-12 08:33:40,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `required_qty` decimal(21,9) not null default 0, MODIFY `consumed_qty` decimal(21,9) not null default 0, MODIFY `current_stock` decimal(21,9) not null default 0
2025-06-12 08:33:40,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` MODIFY `expected_compensation` decimal(21,9) not null default 0, MODIFY `time_to_fill` decimal(21,9)
2025-06-12 08:33:41,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Requisition` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,247 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` MODIFY `task_weight` decimal(21,9) not null default 0
2025-06-12 08:33:41,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,460 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Cycle` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:41,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:42,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Separation Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:44,814 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Request` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabPWA Notification` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` MODIFY `average_rating` decimal(3,2)
2025-06-12 08:33:45,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Feedback` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,572 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-06-12 08:33:45,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:45,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-12 08:33:45,827 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0
2025-06-12 08:33:46,044 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter Template` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,518 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-06-12 08:33:46,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,715 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Assignment` ADD INDEX `creation`(`creation`)
2025-06-12 08:33:46,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` ADD INDEX `creation`(`creation`)
