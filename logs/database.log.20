2025-06-02 11:32:34,094 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`inter_company_stock_transfer` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:35,003 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-06-02 11:32:35,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:32:35,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-06-02 11:32:35,887 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:32:36,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` MODIFY `no_of_hours` decimal(21,9) not null default 0
2025-06-02 11:32:36,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Practitioner` MODIFY `inpatient_visit_charge` decimal(21,9) not null default 0, MODIFY `op_consulting_charge` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS practitioner_name (`practitioner_name`)
2025-06-02 11:32:36,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit` ADD UNIQUE INDEX IF NOT EXISTS healthcare_service_unit_name (`healthcare_service_unit_name`)
2025-06-02 11:32:37,035 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Session` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:32:37,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Record` MODIFY `cash_limit` decimal(21,9) not null default 0
2025-06-02 11:32:37,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:32:37,821 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`gender` varchar(140),
`mobile` varchar(140),
`company` varchar(140),
`appointment` varchar(140),
`posting_datetime` datetime(6),
`practitioner` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`payment_type` varchar(140) default 'Cash',
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`years_of_insurance` int(11) not null default 0,
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `gender`(`gender`),
index `mobile`(`mobile`),
index `company`(`company`),
index `appointment`(`appointment`),
index `posting_datetime`(`posting_datetime`),
index `practitioner`(`practitioner`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `payment_type`(`payment_type`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `naming_series`(`naming_series`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:38,010 WARNING database DDL Query made to DB:
create table `tabHealthcare Discharge Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`discharge_type_name` varchar(140) unique,
`alias` varchar(140),
`discharge_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `discharge_type_id`(`discharge_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:38,198 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`price_list` varchar(140),
`qty` int(11) not null default 0,
`rate` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`department_hsu` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `price_list`(`price_list`),
index `qty`(`qty`),
index `rate`(`rate`),
index `amount`(`amount`),
index `department_hsu`(`department_hsu`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:38,389 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Template` MODIFY `sample_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:32:38,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:32:38,900 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Appointment` MODIFY `appointment_type` varchar(140), MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `daily_limit` decimal(21,9) not null default 0
2025-06-02 11:32:39,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` MODIFY `delivered_quantity` decimal(21,9) not null default 0, MODIFY `occurence_period` decimal(21,9), MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hms_tz_is_discount_percent` decimal(21,9) not null default 0
2025-06-02 11:32:39,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Unit Type` MODIFY `rate` decimal(21,9) not null default 0
2025-06-02 11:32:39,820 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` MODIFY `amount` decimal(21,9) not null default 0
2025-06-02 11:32:40,035 WARNING database DDL Query made to DB:
create table `tabHealthcare Admission Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`admission_type_name` varchar(140) unique,
`alias` varchar(140),
`admission_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `admission_type_id`(`admission_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:40,366 WARNING database DDL Query made to DB:
create table `tabHealthcare Service Request Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_name` varchar(140),
`service_type` varchar(140),
`item_code` varchar(140),
`payor_plan` varchar(140) default 'Cash',
`price_list` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` int(11) not null default 0,
`qty_returned` int(11) not null default 0,
`amount` decimal(21,9) not null default 0,
`payment_type` varchar(140) default 'Cash',
`years_of_insurance` int(11) not null default 0,
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`card_no` varchar(140),
`national_id` varchar(140),
`authorization_number` varchar(140),
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`request_id` varchar(140),
`department_hsu` varchar(140),
`sales_invoice_number` varchar(140),
`lrpmt_doc_created` int(1) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`invoiced` int(1) not null default 0,
`is_cancelled` int(1) not null default 0,
`is_restricted` int(1) not null default 0,
`has_copayment` int(1) not null default 0,
`discount_applied` int(1) not null default 0,
index `service_name`(`service_name`),
index `service_type`(`service_type`),
index `item_code`(`item_code`),
index `payor_plan`(`payor_plan`),
index `price_list`(`price_list`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `payment_type`(`payment_type`),
index `years_of_insurance`(`years_of_insurance`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `card_no`(`card_no`),
index `national_id`(`national_id`),
index `authorization_number`(`authorization_number`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `request_id`(`request_id`),
index `department_hsu`(`department_hsu`),
index `sales_invoice_number`(`sales_invoice_number`),
index `lrpmt_doc_created`(`lrpmt_doc_created`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `invoiced`(`invoiced`),
index `is_cancelled`(`is_cancelled`),
index `is_restricted`(`is_restricted`),
index `has_copayment`(`has_copayment`),
index `discount_applied`(`discount_applied`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:40,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` MODIFY `lab_test_rate` decimal(21,9) not null default 0, MODIFY `c_max_range` decimal(21,9) not null default 0, MODIFY `m_max_range` decimal(21,9) not null default 0, MODIFY `m_min_range` decimal(21,9) not null default 0, MODIFY `c_min_range` decimal(21,9) not null default 0, MODIFY `i_min_range` decimal(21,9) not null default 0, MODIFY `i_max_range` decimal(21,9) not null default 0, MODIFY `f_max_range` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `f_min_range` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS lab_test_code (`lab_test_code`)
2025-06-02 11:32:40,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` ADD INDEX `lab_test_name_index`(`lab_test_name`)
2025-06-02 11:32:40,711 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Template` DROP INDEX `lab_test_name`
2025-06-02 11:32:41,055 WARNING database DDL Query made to DB:
create table `tabHealthcare Facility` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`facility_name` varchar(140) unique,
`facility_code` varchar(140),
`facility_level_code` varchar(140),
`certification_no` varchar(140),
`abbreviation_code` varchar(140),
`classification` varchar(140),
`classification_id` varchar(140),
`ward_code` varchar(140),
`postal_address` text,
`owner_code` varchar(140),
`ownership_type_code` varchar(140),
`pay_to_code` varchar(140),
`percent_sent` decimal(21,9) not null default 0,
`certification_application_date` date,
`status` varchar(140),
`has_eclaims` int(1) not null default 0,
`send_amount_to_msd` int(1) not null default 0,
`key_contact` varchar(140),
`email_address` varchar(140),
`telephone_no` varchar(140),
`fax` varchar(140),
`website` varchar(140),
`longitude` varchar(140),
`latitude` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `facility_code`(`facility_code`),
index `facility_level_code`(`facility_level_code`),
index `certification_no`(`certification_no`),
index `abbreviation_code`(`abbreviation_code`),
index `classification`(`classification`),
index `classification_id`(`classification_id`),
index `ward_code`(`ward_code`),
index `owner_code`(`owner_code`),
index `ownership_type_code`(`ownership_type_code`),
index `pay_to_code`(`pay_to_code`),
index `percent_sent`(`percent_sent`),
index `certification_application_date`(`certification_application_date`),
index `status`(`status`),
index `has_eclaims`(`has_eclaims`),
index `send_amount_to_msd`(`send_amount_to_msd`),
index `key_contact`(`key_contact`),
index `email_address`(`email_address`),
index `telephone_no`(`telephone_no`),
index `fax`(`fax`),
index `website`(`website`),
index `longitude`(`longitude`),
index `latitude`(`latitude`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:41,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiagnosis` MODIFY `estimated_duration` decimal(21,9)
2025-06-02 11:32:41,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter` MODIFY `daily_limit` decimal(21,9) not null default 0, MODIFY `current_total` decimal(21,9) not null default 0, MODIFY `previous_total` decimal(21,9) not null default 0
2025-06-02 11:32:41,670 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedical Department` ADD UNIQUE INDEX IF NOT EXISTS department (`department`)
2025-06-02 11:32:42,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Insurance Coverage` ADD COLUMN `has_copayment` int(1) not null default 0, ADD COLUMN `dosage` varchar(140), ADD COLUMN `strength` varchar(140), ADD COLUMN `maximum_quantity` int(11) not null default 0, ADD COLUMN `maximum_quantity_outpatient` int(11) not null default 0, ADD COLUMN `maximum_quantity_inpatient` int(11) not null default 0
2025-06-02 11:32:42,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Insurance Coverage` MODIFY `maximum_claim_duration` decimal(21,9)
2025-06-02 11:32:42,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Service Insurance Coverage` ADD INDEX `healthcare_service_index`(`healthcare_service`), ADD INDEX `healthcare_service_template_index`(`healthcare_service_template`), ADD INDEX `is_active_index`(`is_active`), ADD INDEX `healthcare_insurance_coverage_plan_index`(`healthcare_insurance_coverage_plan`), ADD INDEX `company_index`(`company`), ADD INDEX `has_copayment_index`(`has_copayment`), ADD INDEX `dosage_index`(`dosage`), ADD INDEX `strength_index`(`strength`), ADD INDEX `maximum_quantity_index`(`maximum_quantity`), ADD INDEX `maximum_quantity_outpatient_index`(`maximum_quantity_outpatient`), ADD INDEX `maximum_quantity_inpatient_index`(`maximum_quantity_inpatient`)
2025-06-02 11:32:42,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Coverage Plan` ADD COLUMN `has_followup_charges` int(1) not null default 0, ADD COLUMN `has_fasttrack_charges` int(1) not null default 0
2025-06-02 11:32:42,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Coverage Plan` MODIFY `daily_limit` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS coverage_plan_name (`coverage_plan_name`)
2025-06-02 11:32:42,616 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Coverage Plan` ADD INDEX `has_followup_charges_index`(`has_followup_charges`), ADD INDEX `has_fasttrack_charges_index`(`has_fasttrack_charges`)
2025-06-02 11:32:42,842 WARNING database DDL Query made to DB:
create table `tabHealthcare Ward Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ward_type_name` varchar(140) unique,
`ward_type_id` int(11) not null default 0,
`item_code` varchar(140),
`notification_required_after` int(11) not null default 0,
`alias` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ward_type_id`(`ward_type_id`),
index `item_code`(`item_code`),
index `notification_required_after`(`notification_required_after`),
index `alias`(`alias`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:43,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-06-02 11:32:43,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Referral` ADD COLUMN `encounter` varchar(140), ADD COLUMN `referral_type` varchar(140), ADD COLUMN `patient` varchar(140), ADD COLUMN `first_name` varchar(140), ADD COLUMN `last_name` varchar(140), ADD COLUMN `national_id` varchar(140), ADD COLUMN `authorization_no` varchar(140), ADD COLUMN `dob` date, ADD COLUMN `attendance_date` date, ADD COLUMN `patient_type_code` varchar(140), ADD COLUMN `insurance_company` varchar(140), ADD COLUMN `referral_date` datetime(6), ADD COLUMN `practitioner_no` varchar(140), ADD COLUMN `referral_id` varchar(140), ADD COLUMN `referral_submitted_by` varchar(140), ADD COLUMN `referral_updated_by` varchar(140)
2025-06-02 11:32:43,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Referral` MODIFY `reason_for_referral` longtext
2025-06-02 11:32:43,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Referral` ADD INDEX `encounter_index`(`encounter`), ADD INDEX `referral_type_index`(`referral_type`), ADD INDEX `posting_date_index`(`posting_date`), ADD INDEX `appointment_index`(`appointment`), ADD INDEX `patient_index`(`patient`), ADD INDEX `first_name_index`(`first_name`), ADD INDEX `last_name_index`(`last_name`), ADD INDEX `patient_name_index`(`patient_name`), ADD INDEX `card_no_index`(`card_no`), ADD INDEX `national_id_index`(`national_id`), ADD INDEX `authorization_no_index`(`authorization_no`), ADD INDEX `dob_index`(`dob`), ADD INDEX `attendance_date_index`(`attendance_date`), ADD INDEX `patient_type_code_index`(`patient_type_code`), ADD INDEX `mobile_no_index`(`mobile_no`), ADD INDEX `insurance_company_index`(`insurance_company`), ADD INDEX `source_facility_index`(`source_facility`), ADD INDEX `source_facility_code_index`(`source_facility_code`), ADD INDEX `referrer_facility_index`(`referrer_facility`), ADD INDEX `referrer_facility_code_index`(`referrer_facility_code`), ADD INDEX `referral_status_index`(`referral_status`), ADD INDEX `referral_date_index`(`referral_date`), ADD INDEX `practitioner_index`(`practitioner`), ADD INDEX `practitioner_no_index`(`practitioner_no`), ADD INDEX `referral_no_index`(`referral_no`), ADD INDEX `referral_id_index`(`referral_id`), ADD INDEX `referral_submitted_by_index`(`referral_submitted_by`), ADD INDEX `referral_updated_by_index`(`referral_updated_by`)
2025-06-02 11:32:43,792 WARNING database DDL Query made to DB:
create table `tabHealthcare Room Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`room_type_name` varchar(140) unique,
`alias` varchar(140),
`room_type_id` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `alias`(`alias`),
index `room_type_id`(`room_type_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:43,997 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`qty` int(11) not null default 0,
`approval_ref_no` varchar(140),
`notes` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `qty`(`qty`),
index `approval_ref_no`(`approval_ref_no`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:44,204 WARNING database DDL Query made to DB:
create table `tabHealthcare Referral diagnosis` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
`disease_code` varchar(140),
`description` text,
index `status`(`status`),
index `disease_code`(`disease_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:44,619 WARNING database DDL Query made to DB:
create table `tabHospital Revenue Entry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`patient` varchar(140),
`patient_name` varchar(140),
`customer` varchar(140),
`appointment` varchar(140),
`source_doctype` varchar(140),
`source_docname` varchar(140),
`posting_date` varchar(140),
`company` varchar(140),
`payment_type` varchar(140),
`mode_of_payment` varchar(140),
`insurance_subscription` varchar(140),
`insurance_company` varchar(140),
`insurance_coverage_plan` varchar(140),
`service_type` varchar(140),
`service_name` varchar(140),
`item_code` varchar(140),
`price_list` varchar(140),
`currency` varchar(140),
`rate` decimal(21,9) not null default 0,
`percent_covered` decimal(21,9) not null default 0,
`qty` decimal(21,9) not null default 0,
`qty_returned` decimal(21,9) not null default 0,
`amount` decimal(21,9) not null default 0,
`lrpmt_doctype` varchar(140),
`lrpmt_docname` varchar(140),
`dn_detail` varchar(140),
`lrpmt_status` varchar(140),
`is_cancelled` int(1) not null default 0,
`ref_doctype` varchar(140),
`ref_docname` varchar(140),
`sales_invoice` varchar(140),
`healthcare_practitioner` varchar(140),
`healthcare_service_unit` varchar(140),
`department` varchar(140),
`cost_center` varchar(140),
`created_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `patient`(`patient`),
index `patient_name`(`patient_name`),
index `customer`(`customer`),
index `appointment`(`appointment`),
index `source_doctype`(`source_doctype`),
index `source_docname`(`source_docname`),
index `posting_date`(`posting_date`),
index `company`(`company`),
index `payment_type`(`payment_type`),
index `mode_of_payment`(`mode_of_payment`),
index `insurance_subscription`(`insurance_subscription`),
index `insurance_company`(`insurance_company`),
index `insurance_coverage_plan`(`insurance_coverage_plan`),
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `item_code`(`item_code`),
index `price_list`(`price_list`),
index `currency`(`currency`),
index `rate`(`rate`),
index `percent_covered`(`percent_covered`),
index `qty`(`qty`),
index `qty_returned`(`qty_returned`),
index `amount`(`amount`),
index `lrpmt_doctype`(`lrpmt_doctype`),
index `lrpmt_docname`(`lrpmt_docname`),
index `dn_detail`(`dn_detail`),
index `lrpmt_status`(`lrpmt_status`),
index `is_cancelled`(`is_cancelled`),
index `ref_doctype`(`ref_doctype`),
index `ref_docname`(`ref_docname`),
index `sales_invoice`(`sales_invoice`),
index `healthcare_practitioner`(`healthcare_practitioner`),
index `healthcare_service_unit`(`healthcare_service_unit`),
index `department`(`department`),
index `cost_center`(`cost_center`),
index `created_by`(`created_by`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:44,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_quantity` decimal(21,9) not null default 0
2025-06-02 11:32:45,126 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Product` ADD COLUMN `schemeid` varchar(140), ADD COLUMN `productdescription` varchar(140), ADD COLUMN `highestorderwithoutreferral` int(11) not null default 0, ADD COLUMN `maximumadmissiondays` varchar(140), ADD COLUMN `requiresnationalid` int(1) not null default 0, ADD COLUMN `usespolicy` int(1) not null default 0
2025-06-02 11:32:45,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Product` ADD INDEX `product_name_index`(`product_name`), ADD INDEX `healthcare_insurance_coverage_plan_index`(`healthcare_insurance_coverage_plan`), ADD INDEX `nhif_product_code_index`(`nhif_product_code`), ADD INDEX `schemeid_index`(`schemeid`), ADD INDEX `productdescription_index`(`productdescription`), ADD INDEX `highestorderwithoutreferral_index`(`highestorderwithoutreferral`), ADD INDEX `company_index`(`company`), ADD INDEX `maximumadmissiondays_index`(`maximumadmissiondays`), ADD INDEX `requiresnationalid_index`(`requiresnationalid`), ADD INDEX `usespolicy_index`(`usespolicy`)
2025-06-02 11:32:45,501 WARNING database DDL Query made to DB:
create table `tabNHIF Co-Payment Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type` varchar(140),
`service_name` varchar(140),
`itemcode` varchar(140),
`scheduleitemid` int(11) not null default 0,
`schemeid` varchar(140),
`yearno` int(11) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type`(`service_type`),
index `service_name`(`service_name`),
index `itemcode`(`itemcode`),
index `scheduleitemid`(`scheduleitemid`),
index `schemeid`(`schemeid`),
index `yearno`(`yearno`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:45,682 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`card_type_id` varchar(140),
`card_type_name` varchar(140),
index `card_type_id`(`card_type_id`),
index `card_type_name`(`card_type_name`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:45,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim` ADD COLUMN `confirmation_code_sent` int(1) not null default 0, ADD COLUMN `confirmation_code` varchar(140), ADD COLUMN `receipt_no` varchar(140), ADD COLUMN `submission_id` varchar(140), ADD COLUMN `hashcode` varchar(140), ADD COLUMN `submission_no` varchar(140), ADD COLUMN `submission_channel` varchar(140), ADD COLUMN `date_submitted` datetime(6), ADD COLUMN `submission_remarks` text
2025-06-02 11:32:45,975 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim` MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `patient_file_no` varchar(140), MODIFY `how_likely_would_you_recommend_our_services_to_others` decimal(3,2), MODIFY `practitioner_no` text
2025-06-02 11:32:46,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim` ADD INDEX `patient_appointment_index`(`patient_appointment`), ADD INDEX `company_index`(`company`), ADD INDEX `posting_date_index`(`posting_date`), ADD INDEX `patient_index`(`patient`), ADD INDEX `patient_name_index`(`patient_name`), ADD INDEX `first_name_index`(`first_name`), ADD INDEX `last_name_index`(`last_name`), ADD INDEX `telephone_no_index`(`telephone_no`), ADD INDEX `date_of_birth_index`(`date_of_birth`), ADD INDEX `gender_index`(`gender`), ADD INDEX `cardno_index`(`cardno`), ADD INDEX `authorization_no_index`(`authorization_no`), ADD INDEX `coverage_plan_name_index`(`coverage_plan_name`), ADD INDEX `inpatient_record_index`(`inpatient_record`), ADD INDEX `allow_changes_index`(`allow_changes`), ADD INDEX `total_amount_index`(`total_amount`), ADD INDEX `is_ready_for_auto_submission_index`(`is_ready_for_auto_submission`), ADD INDEX `reviewed_by_index`(`reviewed_by`), ADD INDEX `confirmation_code_sent_index`(`confirmation_code_sent`), ADD INDEX `confirmation_code_index`(`confirmation_code`), ADD INDEX `receipt_no_index`(`receipt_no`), ADD INDEX `facility_code_index`(`facility_code`), ADD INDEX `claim_year_index`(`claim_year`), ADD INDEX `claim_month_index`(`claim_month`), ADD INDEX `folio_no_index`(`folio_no`), ADD INDEX `serial_no_index`(`serial_no`), ADD INDEX `practitioner_name_index`(`practitioner_name`), ADD INDEX `patient_type_code_index`(`patient_type_code`), ADD INDEX `attendance_date_index`(`attendance_date`), ADD INDEX `attendance_time_index`(`attendance_time`), ADD INDEX `item_crt_by_index`(`item_crt_by`), ADD INDEX `date_admitted_index`(`date_admitted`), ADD INDEX `admitted_time_index`(`admitted_time`), ADD INDEX `date_discharge_index`(`date_discharge`), ADD INDEX `discharge_time_index`(`discharge_time`), ADD INDEX `submission_id_index`(`submission_id`), ADD INDEX `hashcode_index`(`hashcode`), ADD INDEX `naming_series_index`(`naming_series`), ADD INDEX `submission_no_index`(`submission_no`), ADD INDEX `submission_channel_index`(`submission_channel`), ADD INDEX `date_submitted_index`(`date_submitted`), ADD INDEX `hms_tz_claim_appointment_list_index`(`hms_tz_claim_appointment_list`)
2025-06-02 11:32:46,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Custom Excluded Services` ADD COLUMN `excludedforscheme` text
2025-06-02 11:32:46,887 WARNING database DDL Query made to DB:
create table `tabNHIF Monthly Claim` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`claim_year` varchar(140),
`claim_month` int(11) not null default 0,
`status` varchar(140),
`posting_date` datetime(6),
`folio_submitted` int(11) not null default 0,
`total_amount_claimed` decimal(21,9) not null default 0,
`acknowledgement_no` varchar(140),
`date_submitted` datetime(6),
`submitted_by` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `claim_year`(`claim_year`),
index `claim_month`(`claim_month`),
index `status`(`status`),
index `posting_date`(`posting_date`),
index `folio_submitted`(`folio_submitted`),
index `total_amount_claimed`(`total_amount_claimed`),
index `acknowledgement_no`(`acknowledgement_no`),
index `date_submitted`(`date_submitted`),
index `submitted_by`(`submitted_by`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:47,103 WARNING database DDL Query made to DB:
create table `tabHealthcare Points of Care` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`point_of_care_name` varchar(140) unique,
`point_of_care_id` varchar(140),
`point_of_care_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `point_of_care_id`(`point_of_care_id`),
index `point_of_care_code`(`point_of_care_code`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:48,244 WARNING database DDL Query made to DB:
create table `tabNHIF Service Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`service_type_name` varchar(140) unique,
`service_type_id` varchar(140),
`require_nhif_number` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `service_type_id`(`service_type_id`),
index `require_nhif_number`(`require_nhif_number`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:49,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Scheme` ADD INDEX `scheme_name_index`(`scheme_name`)
2025-06-02 11:32:49,477 WARNING database DDL Query made to DB:
create table `tabNHIF Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`itemcode` varchar(140),
`itemtypeid` int(11) not null default 0,
`itemname` varchar(140),
`subgroup` varchar(140),
`strength` varchar(140),
`dosage` varchar(140),
`isactive` int(1) not null default 0,
`isrestricted` int(1) not null default 0,
`calculatedperday` varchar(140),
`servicetypeid` int(11) not null default 0,
`serviceinterval` varchar(140),
`typeofinterval` varchar(140),
`waitingperiod` int(11) not null default 0,
`typeofperiod` varchar(140),
`eligibility` varchar(140),
`commonprice` decimal(21,9) not null default 0,
`percentcovered` decimal(21,9) not null default 0,
`availableinlevels` varchar(140),
`practitionerqualifications` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `itemcode`(`itemcode`),
index `itemtypeid`(`itemtypeid`),
index `itemname`(`itemname`),
index `subgroup`(`subgroup`),
index `strength`(`strength`),
index `dosage`(`dosage`),
index `isactive`(`isactive`),
index `isrestricted`(`isrestricted`),
index `calculatedperday`(`calculatedperday`),
index `servicetypeid`(`servicetypeid`),
index `serviceinterval`(`serviceinterval`),
index `typeofinterval`(`typeofinterval`),
index `waitingperiod`(`waitingperiod`),
index `typeofperiod`(`typeofperiod`),
index `eligibility`(`eligibility`),
index `commonprice`(`commonprice`),
index `percentcovered`(`percentcovered`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:49,697 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Update` ADD COLUMN `timestamp` datetime(6), ADD COLUMN `company` varchar(140), ADD COLUMN `user_id` varchar(140)
2025-06-02 11:32:49,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Update` ADD INDEX `timestamp_index`(`timestamp`), ADD INDEX `current_log_index`(`current_log`), ADD INDEX `company_index`(`company`), ADD INDEX `user_id_index`(`user_id`)
2025-06-02 11:32:49,966 WARNING database DDL Query made to DB:
create table `tabHMS TZ Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`facility_code` varchar(140),
`nhif_user` varchar(140),
`nhif_client_secret` text,
`nhif_grant_type` varchar(140),
`nhif_scope` varchar(140),
`enable_nhif_api` int(1) not null default 0,
`check_patient_info_on_his` int(1) not null default 0,
`validate_service_approval_number_on_lrpm_documents` int(1) not null default 1,
`enable_auto_submit_of_claims` int(1) not null default 0,
`nhif_claim_url` varchar(140),
`nhifservice_url` varchar(140),
`nhif_token_url` varchar(140),
`nhif_token_expiry` datetime(6),
`nhif_token` text,
`submit_claim_month` int(11) not null default 0,
`submit_claim_year` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:50,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Price Package` ADD COLUMN `maximumquantityoutpatient` varchar(140), ADD COLUMN `maximumquantityinpatient` varchar(140)
2025-06-02 11:32:50,193 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Price Package` MODIFY `unitprice` decimal(21,9) not null default 0, MODIFY `itemtypeid` int(11) not null default 0
2025-06-02 11:32:50,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Price Package` ADD INDEX `itemcode_index`(`itemcode`), ADD INDEX `itemtypeid_index`(`itemtypeid`), ADD INDEX `pricecode_index`(`pricecode`), ADD INDEX `schemeid_index`(`schemeid`), ADD INDEX `packageid_index`(`packageid`), ADD INDEX `strength_index`(`strength`), ADD INDEX `dosage_index`(`dosage`), ADD INDEX `unitprice_index`(`unitprice`), ADD INDEX `maximumquantity_index`(`maximumquantity`), ADD INDEX `maximumquantityoutpatient_index`(`maximumquantityoutpatient`), ADD INDEX `maximumquantityinpatient_index`(`maximumquantityinpatient`)
2025-06-02 11:32:50,487 WARNING database DDL Query made to DB:
create table `tabNHIF Patient Claim Practitioner` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`practitioner` varchar(140),
`mct_code` varchar(140),
index `practitioner`(`practitioner`),
index `mct_code`(`mct_code`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:50,659 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim Item` MODIFY `unit_price` decimal(21,9) not null default 0, MODIFY `amount_claimed` decimal(21,9) not null default 0, MODIFY `date_created` datetime(6)
2025-06-02 11:32:50,845 WARNING database DDL Query made to DB:
ALTER TABLE `tabOriginal NHIF Patient Claim Item` MODIFY `date_created` datetime(6), MODIFY `amount_claimed` decimal(21,9) not null default 0, MODIFY `unit_price` decimal(21,9) not null default 0
2025-06-02 11:32:51,053 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Response Log` ADD COLUMN `company` varchar(140), ADD COLUMN `ref_doctype` varchar(140), ADD COLUMN `ref_docname` varchar(140), ADD COLUMN `card_no` varchar(140), ADD COLUMN `authorization_no` varchar(140)
2025-06-02 11:32:51,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Response Log` ADD INDEX `user_id_index`(`user_id`), ADD INDEX `timestamp_index`(`timestamp`), ADD INDEX `request_type_index`(`request_type`), ADD INDEX `request_url_index`(`request_url`), ADD INDEX `status_code_index`(`status_code`), ADD INDEX `ref_doctype_index`(`ref_doctype`), ADD INDEX `ref_docname_index`(`ref_docname`), ADD INDEX `card_no_index`(`card_no`), ADD INDEX `authorization_no_index`(`authorization_no`)
2025-06-02 11:32:51,378 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Claim Reconciliation` ADD COLUMN `erp_number_of_submitted_claims` int(11) not null default 0, ADD COLUMN `erp_total_amount_claimed` decimal(21,9) not null default 0
2025-06-02 11:32:51,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Claim Reconciliation` MODIFY `total_amount_claimed` decimal(21,9) not null default 0
2025-06-02 11:32:51,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Claim Reconciliation` ADD INDEX `status_index`(`status`), ADD INDEX `posting_date_index`(`posting_date`), ADD INDEX `number_of_submitted_claims_index`(`number_of_submitted_claims`), ADD INDEX `total_amount_claimed_index`(`total_amount_claimed`), ADD INDEX `erp_number_of_submitted_claims_index`(`erp_number_of_submitted_claims`), ADD INDEX `erp_total_amount_claimed_index`(`erp_total_amount_claimed`)
2025-06-02 11:32:51,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaging NHIF Price Package` ADD COLUMN `service_type` varchar(140), ADD COLUMN `service_name` varchar(140), ADD COLUMN `hascopayment` int(1) not null default 0, ADD COLUMN `maximumquantityoutpatient` varchar(140), ADD COLUMN `maximumquantityinpatient` varchar(140), ADD COLUMN `fields_changed` longtext, ADD COLUMN `previous_item` text
2025-06-02 11:32:51,815 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaging NHIF Price Package` MODIFY `unitprice` decimal(21,9) not null default 0, MODIFY `isrestricted` int(1) not null default 0
2025-06-02 11:32:51,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaging NHIF Price Package` ADD INDEX `service_type_index`(`service_type`), ADD INDEX `service_name_index`(`service_name`), ADD INDEX `itemcode_index`(`itemcode`), ADD INDEX `itemtypeid_index`(`itemtypeid`), ADD INDEX `strength_index`(`strength`), ADD INDEX `dosage_index`(`dosage`), ADD INDEX `schemeid_index`(`schemeid`), ADD INDEX `packageid_index`(`packageid`), ADD INDEX `pricecode_index`(`pricecode`), ADD INDEX `unitprice_index`(`unitprice`), ADD INDEX `isrestricted_index`(`isrestricted`), ADD INDEX `hascopayment_index`(`hascopayment`), ADD INDEX `maximumquantity_index`(`maximumquantity`), ADD INDEX `maximumquantityoutpatient_index`(`maximumquantityoutpatient`), ADD INDEX `maximumquantityinpatient_index`(`maximumquantityinpatient`)
2025-06-02 11:32:52,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim Disease` MODIFY `date_created` datetime(6)
2025-06-02 11:32:52,337 WARNING database DDL Query made to DB:
create table `tabNHIF Item Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_type_id` int(11) not null default 0 unique,
`type_name` varchar(140),
`alias` varchar(140),
`item_group` varchar(140),
`display_item` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `type_name`(`type_name`),
index `alias`(`alias`),
index `item_group`(`item_group`),
index `display_item`(`display_item`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:52,559 WARNING database DDL Query made to DB:
create table `tabHealthcare Card Verifier` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`verifier_name` varchar(140) unique,
`verifier_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `verifier_id`(`verifier_id`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-02 11:32:52,718 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Settings` ADD COLUMN `header_img` text
2025-06-02 11:32:52,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Settings` ADD UNIQUE INDEX IF NOT EXISTS gateway_name (`gateway_name`)
2025-06-02 11:32:53,071 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Mandate` ADD UNIQUE INDEX IF NOT EXISTS mandate (`mandate`)
2025-06-02 11:32:53,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Prescription` DROP INDEX `modified`
2025-06-02 11:32:53,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Item` DROP INDEX `modified`
2025-06-02 11:32:53,476 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Detail` DROP INDEX `modified`
2025-06-02 11:32:53,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Prescription` DROP INDEX `modified`
2025-06-02 11:32:53,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcedure Prescription` DROP INDEX `modified`
2025-06-02 11:32:53,576 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee OT Component` DROP INDEX `modified`
2025-06-02 11:32:53,612 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip OT Component` DROP INDEX `modified`
2025-06-02 11:32:53,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Item` DROP INDEX `modified`
2025-06-02 11:32:53,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` DROP INDEX `modified`
2025-06-02 11:32:53,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer Details` DROP INDEX `modified`
2025-06-02 11:32:53,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Loan` DROP INDEX `modified`
2025-06-02 11:32:53,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepreciation Schedule` DROP INDEX `modified`
2025-06-02 11:32:53,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List Item` DROP INDEX `modified`
2025-06-02 11:32:53,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Barcode` DROP INDEX `modified`
2025-06-02 11:32:53,879 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Operation` DROP INDEX `modified`
2025-06-02 11:32:53,908 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject User` DROP INDEX `modified`
2025-06-02 11:32:53,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Item` DROP INDEX `modified`
2025-06-02 11:32:53,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Account` DROP INDEX `modified`
2025-06-02 11:32:54,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Taxes and Charges` DROP INDEX `modified`
2025-06-02 11:32:54,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Taxes` DROP INDEX `modified`
2025-06-02 11:32:54,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent Participants` DROP INDEX `modified`
2025-06-02 11:32:54,104 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form Field` DROP INDEX `modified`
2025-06-02 11:32:54,142 WARNING database DDL Query made to DB:
ALTER TABLE `tabOriginal NHIF Patient Claim Item` DROP INDEX `modified`
2025-06-02 11:32:54,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim Item` DROP INDEX `modified`
2025-06-02 11:32:54,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Patient Claim Disease` DROP INDEX `modified`
2025-06-02 11:32:54,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Taxes and Charges` DROP INDEX `modified`
2025-06-02 11:32:54,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Operation` DROP INDEX `modified`
2025-06-02 11:32:54,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` DROP INDEX `modified`
2025-06-02 11:32:54,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` DROP INDEX `modified`
2025-06-02 11:32:54,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` DROP INDEX `modified`
2025-06-02 11:32:54,433 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` DROP INDEX `modified`
2025-06-02 11:32:54,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Default` DROP INDEX `modified`
2025-06-02 11:32:54,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item` DROP INDEX `modified`
2025-06-02 11:32:54,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` DROP INDEX `modified`
2025-06-02 11:32:54,570 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item` DROP INDEX `modified`
2025-06-02 11:32:54,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` DROP INDEX `modified`
2025-06-02 11:32:54,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` DROP INDEX `modified`
2025-06-02 11:32:54,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` DROP INDEX `modified`
2025-06-02 11:32:54,745 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Schedule` DROP INDEX `modified`
2025-06-02 11:32:54,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaging NHIF Price Package` DROP INDEX `modified`
2025-06-02 11:32:54,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Stock Item` DROP INDEX `modified`
2025-06-02 11:32:54,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Item` DROP INDEX `modified`
2025-06-02 11:32:54,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order Item` DROP INDEX `modified`
2025-06-02 11:32:54,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacked Item` DROP INDEX `modified`
2025-06-02 11:32:54,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` DROP INDEX `modified`
2025-06-02 11:32:55,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withheld Vouchers` DROP INDEX `modified`
2025-06-02 11:32:55,049 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note Item` DROP INDEX `modified`
2025-06-02 11:32:55,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` DROP INDEX `modified`
2025-06-02 11:32:55,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Advance` DROP INDEX `modified`
2025-06-02 11:32:55,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Payment Reconciliation Log Allocations` DROP INDEX `modified`
2025-06-02 11:32:55,196 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Taxes and Charges` DROP INDEX `modified`
2025-06-02 11:32:55,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` DROP INDEX `modified`
2025-06-02 11:32:55,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Advance` DROP INDEX `modified`
2025-06-02 11:32:55,312 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Advance` DROP INDEX `modified`
2025-06-02 11:32:55,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Finance Book` DROP INDEX `modified`
2025-06-02 11:32:55,397 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Item` DROP INDEX `modified`
2025-06-02 11:32:55,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Item` DROP INDEX `modified`
2025-06-02 11:32:55,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Service Item` DROP INDEX `modified`
2025-06-02 11:32:55,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Variant Attribute` DROP INDEX `modified`
2025-06-02 11:32:55,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Item` DROP INDEX `modified`
2025-06-02 11:32:55,591 WARNING database DDL Query made to DB:
ALTER TABLE `tabNavbar Item` DROP INDEX `modified`
2025-06-02 11:32:55,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Deduction` DROP INDEX `modified`
2025-06-02 11:32:55,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Detail` DROP INDEX `modified`
2025-06-02 11:32:55,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Taxes and Charges` DROP INDEX `modified`
2025-06-02 11:32:55,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomize Form Field` DROP INDEX `modified`
2025-06-02 11:32:55,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Link` DROP INDEX `modified`
2025-06-02 11:32:55,878 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Package Consultation` DROP INDEX `modified`
2025-06-02 11:32:55,923 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Drug Prescription` DROP INDEX `modified`
2025-06-02 11:32:55,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabVC Practitioner` DROP INDEX `modified`
2025-06-02 11:32:56,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Radiology Procedure Prescription` DROP INDEX `modified`
2025-06-02 11:32:56,033 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Therapy Plan Detail` DROP INDEX `modified`
2025-06-02 11:32:56,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabVC Excluded Service Rate` DROP INDEX `modified`
2025-06-02 11:32:56,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabOriginal Delivery Note Item` DROP INDEX `modified`
2025-06-02 11:32:56,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabVC Insurance Rate` DROP INDEX `modified`
2025-06-02 11:32:56,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Package Order Consultation` DROP INDEX `modified`
2025-06-02 11:32:56,272 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Procedure Prescription` DROP INDEX `modified`
2025-06-02 11:32:56,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabVC LRPMT Submitter` DROP INDEX `modified`
2025-06-02 11:32:56,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Diet Recommendation` DROP INDEX `modified`
2025-06-02 11:32:56,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabNHIF Claim Reconciliation Detail` DROP INDEX `modified`
2025-06-02 11:32:56,400 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrevious Lab Prescription` DROP INDEX `modified`
2025-06-02 11:32:56,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabChronic Medications` DROP INDEX `modified`
2025-06-02 11:32:56,483 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaging NHIF Excluded Services` DROP INDEX `modified`
2025-06-02 11:32:56,524 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Discount Item` DROP INDEX `modified`
2025-06-02 11:32:56,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Package Item` DROP INDEX `modified`
2025-06-02 11:32:56,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabVC Cash Rate` DROP INDEX `modified`
2025-06-02 11:32:56,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Bundle Item` DROP INDEX `modified`
2025-06-02 11:32:56,717 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Return` DROP INDEX `modified`
2025-06-02 11:32:56,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabRadiology Procedure Prescription` DROP INDEX `modified`
2025-06-02 11:32:56,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Company Option` DROP INDEX `modified`
2025-06-02 11:32:56,858 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication Return` DROP INDEX `modified`
2025-06-02 11:32:56,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Consultancy` DROP INDEX `modified`
2025-06-02 11:32:56,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabInsurance Claim` DROP INDEX `modified`
2025-06-02 11:32:56,989 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllowed Price List` DROP INDEX `modified`
2025-06-02 11:32:57,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllergy Interaction` DROP INDEX `modified`
2025-06-02 11:32:57,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrug Interaction` DROP INDEX `modified`
2025-06-02 11:32:57,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiet Recommendation` DROP INDEX `modified`
2025-06-02 11:32:57,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Return` DROP INDEX `modified`
2025-06-02 11:32:57,211 WARNING database DDL Query made to DB:
ALTER TABLE `tabMedication Class Interaction` DROP INDEX `modified`
2025-06-02 11:32:57,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Insurance Payment Request Item` DROP INDEX `modified`
2025-06-02 11:32:57,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Return` DROP INDEX `modified`
2025-06-02 11:32:57,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabSample Collection Lab Test Details` DROP INDEX `modified`
2025-06-02 11:32:57,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabClinical Procedure Nursing Task` DROP INDEX `modified`
2025-06-02 11:32:57,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Price Discount` DROP INDEX `modified`
2025-06-02 11:32:57,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabPromotional Scheme Product Discount` DROP INDEX `modified`
2025-06-02 11:32:57,513 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Employee Detail` DROP INDEX `modified`
2025-06-02 11:32:57,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Additional Costs` DROP INDEX `modified`
2025-06-02 11:32:57,593 WARNING database DDL Query made to DB:
ALTER TABLE `tabCSF TZ Bank Charges Detail` DROP INDEX `modified`
2025-06-02 11:32:57,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Document Type` DROP INDEX `modified`
2025-06-02 11:32:57,709 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Proof Submission Detail` DROP INDEX `modified`
2025-06-02 11:32:57,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair Consumed Item` DROP INDEX `modified`
2025-06-02 11:32:57,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item` DROP INDEX `modified`
2025-06-02 11:32:57,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Quotation Item` DROP INDEX `modified`
2025-06-02 11:32:57,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request Costing` DROP INDEX `modified`
2025-06-02 11:32:57,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Time Log` DROP INDEX `modified`
2025-06-02 11:32:57,982 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Item` DROP INDEX `modified`
2025-06-02 11:32:58,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabPSOA Cost Center` DROP INDEX `modified`
2025-06-02 11:32:58,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabOAuth Client Role` DROP INDEX `modified`
2025-06-02 11:32:58,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Outstanding Statement` DROP INDEX `modified`
2025-06-02 11:32:58,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabFull and Final Asset` DROP INDEX `modified`
2025-06-02 11:32:58,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Receipt Supplied Item` DROP INDEX `modified`
2025-06-02 11:32:58,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Itinerary` DROP INDEX `modified`
2025-06-02 11:32:58,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Result Employee` DROP INDEX `modified`
2025-06-02 11:32:58,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabTraining Event Employee` DROP INDEX `modified`
2025-06-02 11:32:58,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabTaxable Salary Slab` DROP INDEX `modified`
2025-06-02 11:32:58,430 WARNING database DDL Query made to DB:
ALTER TABLE `tabStaffing Plan Detail` DROP INDEX `modified`
2025-06-02 11:32:58,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabSkill Assessment` DROP INDEX `modified`
2025-06-02 11:32:58,515 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Timesheet` DROP INDEX `modified`
2025-06-02 11:32:58,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip Leave` DROP INDEX `modified`
2025-06-02 11:32:58,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` DROP INDEX `modified`
2025-06-02 11:32:58,626 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component Account` DROP INDEX `modified`
2025-06-02 11:32:58,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Period Date` DROP INDEX `modified`
2025-06-02 11:32:58,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Policy Detail` DROP INDEX `modified`
2025-06-02 11:32:58,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Date` DROP INDEX `modified`
2025-06-02 11:32:58,765 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Block List Allow` DROP INDEX `modified`
2025-06-02 11:32:58,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer Term` DROP INDEX `modified`
2025-06-02 11:32:58,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterviewer` DROP INDEX `modified`
2025-06-02 11:32:58,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabInterview Detail` DROP INDEX `modified`
2025-06-02 11:32:58,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncome Tax Slab Other Charges` DROP INDEX `modified`
2025-06-02 11:32:58,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule Slab` DROP INDEX `modified`
2025-06-02 11:32:59,017 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Applicable Component` DROP INDEX `modified`
2025-06-02 11:32:59,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpense Claim Account` DROP INDEX `modified`
2025-06-02 11:32:59,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabExpected Skill Set` DROP INDEX `modified`
2025-06-02 11:32:59,150 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Training` DROP INDEX `modified`
2025-06-02 11:32:59,189 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Tax Exemption Declaration Category` DROP INDEX `modified`
2025-06-02 11:32:59,221 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Skill` DROP INDEX `modified`
2025-06-02 11:32:59,256 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Property History` DROP INDEX `modified`
2025-06-02 11:32:59,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Feedback Rating` DROP INDEX `modified`
2025-06-02 11:32:59,328 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Cost Center` DROP INDEX `modified`
2025-06-02 11:32:59,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Boarding Activity` DROP INDEX `modified`
2025-06-02 11:32:59,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Benefit Application Detail` DROP INDEX `modified`
2025-06-02 11:32:59,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabDesignation Skill` DROP INDEX `modified`
2025-06-02 11:32:59,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabDepartment Approver` DROP INDEX `modified`
2025-06-02 11:32:59,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabDaily Work Summary Group User` DROP INDEX `modified`
2025-06-02 11:32:59,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisee` DROP INDEX `modified`
2025-06-02 11:32:59,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Template Goal` DROP INDEX `modified`
2025-06-02 11:32:59,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal KRA` DROP INDEX `modified`
2025-06-02 11:32:59,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal Goal` DROP INDEX `modified`
2025-06-02 11:32:59,774 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Letter content` DROP INDEX `modified`
2025-06-02 11:32:59,821 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Health Monitor Company` DROP INDEX `modified`
2025-06-02 11:32:59,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Parcel` DROP INDEX `modified`
2025-06-02 11:32:59,911 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Service Item` DROP INDEX `modified`
2025-06-02 11:32:59,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabBudget Account` DROP INDEX `modified`
2025-06-02 11:32:59,991 WARNING database DDL Query made to DB:
ALTER TABLE `tabStanbic Payments Info` DROP INDEX `modified`
2025-06-02 11:33:00,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Sub Assembly Item` DROP INDEX `modified`
2025-06-02 11:33:00,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Purchase Receipt` DROP INDEX `modified`
2025-06-02 11:33:00,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` DROP INDEX `modified`
2025-06-02 11:33:00,170 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlanket Order Item` DROP INDEX `modified`
2025-06-02 11:33:00,205 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Plan Item` DROP INDEX `modified`
2025-06-02 11:33:00,255 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record Details` DROP INDEX `modified`
2025-06-02 11:33:00,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` DROP INDEX `modified`
2025-06-02 11:33:00,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransaction Deletion Record Item` DROP INDEX `modified`
2025-06-02 11:33:00,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Payment` DROP INDEX `modified`
2025-06-02 11:33:00,423 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Taxes and Charges` DROP INDEX `modified`
2025-06-02 11:33:00,464 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Item Details` DROP INDEX `modified`
2025-06-02 11:33:00,509 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Exchange Non Stock Item Details` DROP INDEX `modified`
2025-06-02 11:33:00,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabTop Bar Item` DROP INDEX `modified`
2025-06-02 11:33:00,598 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Visit Purpose` DROP INDEX `modified`
2025-06-02 11:33:00,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllowed To Transact With` DROP INDEX `modified`
2025-06-02 11:33:00,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Explosion Item` DROP INDEX `modified`
2025-06-02 11:33:00,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Route Redirect` DROP INDEX `modified`
2025-06-02 11:33:00,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Item` DROP INDEX `modified`
2025-06-02 11:33:00,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrice Change Request Detail` DROP INDEX `modified`
2025-06-02 11:33:00,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Header` DROP INDEX `modified`
2025-06-02 11:33:00,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabReview Level` DROP INDEX `modified`
2025-06-02 11:33:00,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity Item` DROP INDEX `modified`
2025-06-02 11:33:00,985 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Allowed Types` DROP INDEX `modified`
2025-06-02 11:33:01,030 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation Working Hour` DROP INDEX `modified`
2025-06-02 11:33:01,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabTask Depends On` DROP INDEX `modified`
2025-06-02 11:33:01,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Stop` DROP INDEX `modified`
2025-06-02 11:33:01,158 WARNING database DDL Query made to DB:
ALTER TABLE `tabUnreconcile Payment Entries` DROP INDEX `modified`
2025-06-02 11:33:01,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Review Objective` DROP INDEX `modified`
2025-06-02 11:33:01,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Goal Objective` DROP INDEX `modified`
2025-06-02 11:33:01,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabHealthcare Schedule Time Slot` DROP INDEX `modified`
2025-06-02 11:33:01,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Accounting Ledger Items` DROP INDEX `modified`
2025-06-02 11:33:01,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Type Service Item` DROP INDEX `modified`
2025-06-02 11:33:01,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabExchange Rate Revaluation Account` DROP INDEX `modified`
2025-06-02 11:33:01,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Reorder` DROP INDEX `modified`
2025-06-02 11:33:01,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport Filter` DROP INDEX `modified`
2025-06-02 11:33:01,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabParty Account` DROP INDEX `modified`
2025-06-02 11:33:01,615 WARNING database DDL Query made to DB:
ALTER TABLE `tabForm Tour Step` DROP INDEX `modified`
2025-06-02 11:33:01,656 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Custom Block` DROP INDEX `modified`
2025-06-02 11:33:01,701 WARNING database DDL Query made to DB:
ALTER TABLE `tabPacking Slip Item` DROP INDEX `modified`
2025-06-02 11:33:01,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabProcess Statement Of Accounts Customer` DROP INDEX `modified`
2025-06-02 11:33:01,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabNote Seen By` DROP INDEX `modified`
2025-06-02 11:33:01,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Item Reference` DROP INDEX `modified`
2025-06-02 11:33:01,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Maintenance Task` DROP INDEX `modified`
2025-06-02 11:33:01,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Item` DROP INDEX `modified`
2025-06-02 11:33:01,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstallation Note Item` DROP INDEX `modified`
2025-06-02 11:33:02,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabCodification Table` DROP INDEX `modified`
2025-06-02 11:33:02,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Number Card` DROP INDEX `modified`
2025-06-02 11:33:02,117 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Assessment Sheet` DROP INDEX `modified`
2025-06-02 11:33:02,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Scrap Item` DROP INDEX `modified`
2025-06-02 11:33:02,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Credit Limit` DROP INDEX `modified`
2025-06-02 11:33:02,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Payment Ledger Items` DROP INDEX `modified`
2025-06-02 11:33:02,303 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Supplier` DROP INDEX `modified`
2025-06-02 11:33:02,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Queue Recipient` DROP INDEX `modified`
2025-06-02 11:33:02,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubcontracting Order Supplied Item` DROP INDEX `modified`
2025-06-02 11:33:02,408 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form List Column` DROP INDEX `modified`
2025-06-02 11:33:02,443 WARNING database DDL Query made to DB:
ALTER TABLE `tabSQL Process Detail` DROP INDEX `modified`
2025-06-02 11:33:02,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabHas Role` DROP INDEX `modified`
2025-06-02 11:33:02,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Social Login` DROP INDEX `modified`
2025-06-02 11:33:02,547 WARNING database DDL Query made to DB:
ALTER TABLE `tabHas Domain` DROP INDEX `modified`
2025-06-02 11:33:02,580 WARNING database DDL Query made to DB:
ALTER TABLE `tabBlock Module` DROP INDEX `modified`
2025-06-02 11:33:02,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Data` DROP INDEX `modified`
2025-06-02 11:33:02,649 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany History` DROP INDEX `modified`
2025-06-02 11:33:02,683 WARNING database DDL Query made to DB:
ALTER TABLE `tabAbout Us Team Member` DROP INDEX `modified`
2025-06-02 11:33:02,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabPortal Menu Item` DROP INDEX `modified`
2025-06-02 11:33:02,768 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Sidebar Item` DROP INDEX `modified`
2025-06-02 11:33:02,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Meta Tag` DROP INDEX `modified`
2025-06-02 11:33:02,852 WARNING database DDL Query made to DB:
ALTER TABLE `tabNursing Checklist Template Task` DROP INDEX `modified`
2025-06-02 11:33:02,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabLDAP Group Mapping` DROP INDEX `modified`
2025-06-02 11:33:02,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabDriving License Category` DROP INDEX `modified`
2025-06-02 11:33:02,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduct Bundle Item` DROP INDEX `modified`
2025-06-02 11:33:03,033 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page Block` DROP INDEX `modified`
2025-06-02 11:33:03,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabLogs To Clear` DROP INDEX `modified`
2025-06-02 11:33:03,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM Update Batch` DROP INDEX `modified`
2025-06-02 11:33:03,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabCRM Note` DROP INDEX `modified`
2025-06-02 11:33:03,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Email` DROP INDEX `modified`
2025-06-02 11:33:03,252 WARNING database DDL Query made to DB:
ALTER TABLE `tabPossible Root Cause` DROP INDEX `modified`
2025-06-02 11:33:03,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoot Cause Prevention Strategy` DROP INDEX `modified`
2025-06-02 11:33:03,326 WARNING database DDL Query made to DB:
ALTER TABLE `tabReporting Currency Settings Rate` DROP INDEX `modified`
2025-06-02 11:33:03,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Quick List` DROP INDEX `modified`
2025-06-02 11:33:03,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Vehicle Detail` DROP INDEX `modified`
2025-06-02 11:33:03,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Company Detail` DROP INDEX `modified`
2025-06-02 11:33:03,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabTZ Insurance Policy Holder Detail` DROP INDEX `modified`
2025-06-02 11:33:03,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect Lead` DROP INDEX `modified`
2025-06-02 11:33:03,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabProspect Opportunity` DROP INDEX `modified`
2025-06-02 11:33:03,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Filter Field` DROP INDEX `modified`
2025-06-02 11:33:03,653 WARNING database DDL Query made to DB:
ALTER TABLE `tabVariant Field` DROP INDEX `modified`
2025-06-02 11:33:03,699 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Invoice Reference` DROP INDEX `modified`
2025-06-02 11:33:03,737 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpening Invoice Creation Tool Item` DROP INDEX `modified`
2025-06-02 11:33:03,783 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Action Permitted Role` DROP INDEX `modified`
2025-06-02 11:33:03,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabCash Flow Mapping Template Details` DROP INDEX `modified`
2025-06-02 11:33:03,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabTimesheet Detail` DROP INDEX `modified`
2025-06-02 11:33:03,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Group Table` DROP INDEX `modified`
2025-06-02 11:33:03,955 WARNING database DDL Query made to DB:
ALTER TABLE `tabCost Center Allocation Percentage` DROP INDEX `modified`
2025-06-02 11:33:04,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabTax Withholding Rate` DROP INDEX `modified`
2025-06-02 11:33:04,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Employee Salary Slip` DROP INDEX `modified`
2025-06-02 11:33:04,086 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType State` DROP INDEX `modified`
2025-06-02 11:33:04,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabKanban Board Column` DROP INDEX `modified`
2025-06-02 11:33:04,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabLedger Merge Accounts` DROP INDEX `modified`
2025-06-02 11:33:04,217 WARNING database DDL Query made to DB:
ALTER TABLE `tabNewsletter Email Group` DROP INDEX `modified`
2025-06-02 11:33:04,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabNewsletter Attachment` DROP INDEX `modified`
2025-06-02 11:33:04,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Tax` DROP INDEX `modified`
2025-06-02 11:33:04,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Team` DROP INDEX `modified`
2025-06-02 11:33:04,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency Exchange Settings Details` DROP INDEX `modified`
2025-06-02 11:33:04,449 WARNING database DDL Query made to DB:
ALTER TABLE `tabCurrency Exchange Settings Result` DROP INDEX `modified`
2025-06-02 11:33:04,493 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompetitor Detail` DROP INDEX `modified`
2025-06-02 11:33:04,540 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Timesheet` DROP INDEX `modified`
2025-06-02 11:33:04,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabFiscal Year Company` DROP INDEX `modified`
2025-06-02 11:33:04,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabIMAP Folder` DROP INDEX `modified`
2025-06-02 11:33:04,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Details` DROP INDEX `modified`
2025-06-02 11:33:04,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabParking Bill Items` DROP INDEX `modified`
2025-06-02 11:33:04,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaintenance Schedule Detail` DROP INDEX `modified`
2025-06-02 11:33:04,846 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` DROP INDEX `modified`
2025-06-02 11:33:04,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Capitalization Asset Item` DROP INDEX `modified`
2025-06-02 11:33:04,941 WARNING database DDL Query made to DB:
ALTER TABLE `tabTreatment Plan Template Item` DROP INDEX `modified`
2025-06-02 11:33:04,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Quality Inspection Parameter` DROP INDEX `modified`
2025-06-02 11:33:05,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Link` DROP INDEX `modified`
2025-06-02 11:33:05,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Salary Component Limit` DROP INDEX `modified`
2025-06-02 11:33:05,122 WARNING database DDL Query made to DB:
ALTER TABLE `tabSub Operation` DROP INDEX `modified`
2025-06-02 11:33:05,163 WARNING database DDL Query made to DB:
ALTER TABLE `tabSouth Africa VAT Account` DROP INDEX `modified`
2025-06-02 11:33:05,201 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Piecework Additional Salary` DROP INDEX `modified`
2025-06-02 11:33:05,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Level Priority` DROP INDEX `modified`
2025-06-02 11:33:05,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Receipt Item Supplied` DROP INDEX `modified`
2025-06-02 11:33:05,344 WARNING database DDL Query made to DB:
ALTER TABLE `tabTreatment Plan Template Practitioner` DROP INDEX `modified`
2025-06-02 11:33:05,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order Item Supplied` DROP INDEX `modified`
2025-06-02 11:33:05,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem Tax` DROP INDEX `modified`
2025-06-02 11:33:05,480 WARNING database DDL Query made to DB:
ALTER TABLE `tabSLA Fulfilled On Status` DROP INDEX `modified`
2025-06-02 11:33:05,526 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Closing Entry Detail` DROP INDEX `modified`
2025-06-02 11:33:05,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Layout Field` DROP INDEX `modified`
2025-06-02 11:33:05,609 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Item` DROP INDEX `modified`
2025-06-02 11:33:05,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier Group Item` DROP INDEX `modified`
2025-06-02 11:33:05,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabCampaign Item` DROP INDEX `modified`
2025-06-02 11:33:05,742 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Partner Item` DROP INDEX `modified`
2025-06-02 11:33:05,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabTerritory Item` DROP INDEX `modified`
2025-06-02 11:33:05,831 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer Group Item` DROP INDEX `modified`
2025-06-02 11:33:05,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabSingle Piecework Employees` DROP INDEX `modified`
2025-06-02 11:33:05,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabStation Members` DROP INDEX `modified`
2025-06-02 11:33:05,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabPersonal Data Deletion Step` DROP INDEX `modified`
2025-06-02 11:33:06,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Item` DROP INDEX `modified`
2025-06-02 11:33:06,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Search Fields` DROP INDEX `modified`
2025-06-02 11:33:06,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Group Member` DROP INDEX `modified`
2025-06-02 11:33:06,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Occupancy` DROP INDEX `modified`
2025-06-02 11:33:06,186 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Operation` DROP INDEX `modified`
2025-06-02 11:33:06,237 WARNING database DDL Query made to DB:
ALTER TABLE `tabProject Template Task` DROP INDEX `modified`
2025-06-02 11:33:06,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Terms Template Detail` DROP INDEX `modified`
2025-06-02 11:33:06,339 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Attribute` DROP INDEX `modified`
2025-06-02 11:33:06,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabHomepage Featured Product` DROP INDEX `modified`
2025-06-02 11:33:06,429 WARNING database DDL Query made to DB:
ALTER TABLE `tabPiecework Payment Allocation` DROP INDEX `modified`
2025-06-02 11:33:06,473 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Invoice` DROP INDEX `modified`
2025-06-02 11:33:06,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Inspection Reading` DROP INDEX `modified`
2025-06-02 11:33:06,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` DROP INDEX `modified`
2025-06-02 11:33:06,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Type Module` DROP INDEX `modified`
2025-06-02 11:33:06,644 WARNING database DDL Query made to DB:
ALTER TABLE `tabWork Order Consignment Detail` DROP INDEX `modified`
2025-06-02 11:33:06,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Consignment Detail` DROP INDEX `modified`
2025-06-02 11:33:06,714 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser Select Document Type` DROP INDEX `modified`
2025-06-02 11:33:06,753 WARNING database DDL Query made to DB:
ALTER TABLE `tabPledge` DROP INDEX `modified`
2025-06-02 11:33:06,792 WARNING database DDL Query made to DB:
ALTER TABLE `tabUnpledge` DROP INDEX `modified`
2025-06-02 11:33:06,832 WARNING database DDL Query made to DB:
ALTER TABLE `tabProposed Pledge` DROP INDEX `modified`
2025-06-02 11:33:06,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Chart` DROP INDEX `modified`
2025-06-02 11:33:06,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipment Delivery Note` DROP INDEX `modified`
2025-06-02 11:33:06,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient History Standard Document Type` DROP INDEX `modified`
2025-06-02 11:33:06,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient History Custom Document Type` DROP INDEX `modified`
2025-06-02 11:33:07,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Payment` DROP INDEX `modified`
2025-06-02 11:33:07,060 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Reconciliation Pro Invoice` DROP INDEX `modified`
2025-06-02 11:33:07,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabAllowed Dimension` DROP INDEX `modified`
2025-06-02 11:33:07,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabApplicable On Account` DROP INDEX `modified`
2025-06-02 11:33:07,180 WARNING database DDL Query made to DB:
ALTER TABLE `tabIncoming Call Handling Schedule` DROP INDEX `modified`
2025-06-02 11:33:07,214 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuery Parameters` DROP INDEX `modified`
2025-06-02 11:33:07,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Clearance Pro Detail` DROP INDEX `modified`
2025-06-02 11:33:07,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabAuto Repeat Day` DROP INDEX `modified`
2025-06-02 11:33:07,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequest for Quotation Supplier` DROP INDEX `modified`
2025-06-02 11:33:07,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabExercise` DROP INDEX `modified`
2025-06-02 11:33:07,451 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Attachment` DROP INDEX `modified`
2025-06-02 11:33:07,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Feedback Parameter` DROP INDEX `modified`
2025-06-02 11:33:07,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Procedure Process` DROP INDEX `modified`
2025-06-02 11:33:07,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Template Field` DROP INDEX `modified`
2025-06-02 11:33:07,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabProduction Plan Material Request Warehouse` DROP INDEX `modified`
2025-06-02 11:33:07,637 WARNING database DDL Query made to DB:
ALTER TABLE `tabLoan Repayment Detail` DROP INDEX `modified`
2025-06-02 11:33:07,680 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuality Action Resolution` DROP INDEX `modified`
2025-06-02 11:33:07,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Payment Method` DROP INDEX `modified`
2025-06-02 11:33:07,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabInv ERR Detail` DROP INDEX `modified`
2025-06-02 11:33:07,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabLog Setting User` DROP INDEX `modified`
2025-06-02 11:33:07,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabTherapy Plan Template Detail` DROP INDEX `modified`
2025-06-02 11:33:07,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Entry Detail` DROP INDEX `modified`
2025-06-02 11:33:07,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabInpatient Medication Order Entry` DROP INDEX `modified`
2025-06-02 11:33:07,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabAssignment Rule User` DROP INDEX `modified`
2025-06-02 11:33:07,996 WARNING database DDL Query made to DB:
ALTER TABLE `tabUAE VAT Account` DROP INDEX `modified`
2025-06-02 11:33:08,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType Action` DROP INDEX `modified`
2025-06-02 11:33:08,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Theme Ignore App` DROP INDEX `modified`
2025-06-02 11:33:08,130 WARNING database DDL Query made to DB:
ALTER TABLE `tabDependent Task` DROP INDEX `modified`
2025-06-02 11:33:08,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Slideshow Item` DROP INDEX `modified`
2025-06-02 11:33:08,226 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocument Naming Rule Condition` DROP INDEX `modified`
2025-06-02 11:33:08,273 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Order Reference` DROP INDEX `modified`
2025-06-02 11:33:08,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport Column` DROP INDEX `modified`
2025-06-02 11:33:08,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Recipient` DROP INDEX `modified`
2025-06-02 11:33:08,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Digest Recipient` DROP INDEX `modified`
2025-06-02 11:33:08,481 WARNING database DDL Query made to DB:
ALTER TABLE `tabPSOA Project` DROP INDEX `modified`
2025-06-02 11:33:08,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabLab Test Group Template` DROP INDEX `modified`
2025-06-02 11:33:08,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabQuotation Lost Reason Detail` DROP INDEX `modified`
2025-06-02 11:33:08,620 WARNING database DDL Query made to DB:
ALTER TABLE `tabOpportunity Lost Reason Detail` DROP INDEX `modified`
2025-06-02 11:33:08,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabDescriptive Test Result` DROP INDEX `modified`
2025-06-02 11:33:08,703 WARNING database DDL Query made to DB:
ALTER TABLE `tabOAuth Scope` DROP INDEX `modified`
2025-06-02 11:33:08,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabDunning Letter Text` DROP INDEX `modified`
2025-06-02 11:33:08,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Result` DROP INDEX `modified`
2025-06-02 11:33:08,844 WARNING database DDL Query made to DB:
ALTER TABLE `tabService Day` DROP INDEX `modified`
2025-06-02 11:33:08,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabSpecial Closing Balance Detail` DROP INDEX `modified`
2025-06-02 11:33:08,936 WARNING database DDL Query made to DB:
ALTER TABLE `tabDescriptive Test Template` DROP INDEX `modified`
2025-06-02 11:33:08,980 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepack Template Detail` DROP INDEX `modified`
2025-06-02 11:33:09,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabNormal Test Template` DROP INDEX `modified`
2025-06-02 11:33:09,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabSubscription Plan Detail` DROP INDEX `modified`
2025-06-02 11:33:09,116 WARNING database DDL Query made to DB:
ALTER TABLE `tabPause SLA On Status` DROP INDEX `modified`
2025-06-02 11:33:09,165 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Opening Entry Detail` DROP INDEX `modified`
2025-06-02 11:33:09,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile User` DROP INDEX `modified`
2025-06-02 11:33:09,243 WARNING database DDL Query made to DB:
ALTER TABLE `tabInstalled Application` DROP INDEX `modified`
2025-06-02 11:33:09,290 WARNING database DDL Query made to DB:
ALTER TABLE `tabSocial Link Settings` DROP INDEX `modified`
2025-06-02 11:33:09,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabOnboarding Permission` DROP INDEX `modified`
2025-06-02 11:33:09,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabOnboarding Step Map` DROP INDEX `modified`
2025-06-02 11:33:09,425 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Template Account` DROP INDEX `modified`
2025-06-02 11:33:09,470 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Assessment Detail` DROP INDEX `modified`
2025-06-02 11:33:09,514 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card Link` DROP INDEX `modified`
2025-06-02 11:33:09,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabHoliday` DROP INDEX `modified`
2025-06-02 11:33:09,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabBody Part Link` DROP INDEX `modified`
2025-06-02 11:33:09,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact Phone` DROP INDEX `modified`
2025-06-02 11:33:09,685 WARNING database DDL Query made to DB:
ALTER TABLE `tabExercise Type Step` DROP INDEX `modified`
2025-06-02 11:33:09,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabMachine Strip Request Item` DROP INDEX `modified`
2025-06-02 11:33:09,771 WARNING database DDL Query made to DB:
ALTER TABLE `tabFee Validity Reference` DROP INDEX `modified`
2025-06-02 11:33:09,817 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart Link` DROP INDEX `modified`
2025-06-02 11:33:09,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabEFD Z Report Invoice` DROP INDEX `modified`
2025-06-02 11:33:09,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart Field` DROP INDEX `modified`
2025-06-02 11:33:09,954 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter Diagnosis` DROP INDEX `modified`
2025-06-02 11:33:09,995 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Encounter Symptom` DROP INDEX `modified`
2025-06-02 11:33:10,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabFuel Request Table` DROP INDEX `modified`
2025-06-02 11:33:10,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabDiscounted Invoice` DROP INDEX `modified`
2025-06-02 11:33:10,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabPatient Relation` DROP INDEX `modified`
2025-06-02 11:33:10,188 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Transaction Payments` DROP INDEX `modified`
2025-06-02 11:33:10,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal Sheet Template Detail` DROP INDEX `modified`
2025-06-02 11:33:10,286 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoal Sheet Detail` DROP INDEX `modified`
2025-06-02 11:33:10,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Area` DROP INDEX `modified`
2025-06-02 11:33:10,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Detail` DROP INDEX `modified`
2025-06-02 11:33:10,421 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Issue Detail` DROP INDEX `modified`
2025-06-02 11:33:10,467 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppointment Booking Slots` DROP INDEX `modified`
2025-06-02 11:33:10,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabDynamic Link` DROP INDEX `modified`
2025-06-02 11:33:10,555 WARNING database DDL Query made to DB:
ALTER TABLE `tabNotification Subscribed Document` DROP INDEX `modified`
2025-06-02 11:33:10,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Movement Item` DROP INDEX `modified`
2025-06-02 11:33:10,657 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrganism Test Item` DROP INDEX `modified`
2025-06-02 11:33:10,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabOrganism Test Result` DROP INDEX `modified`
2025-06-02 11:33:10,722 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Details` DROP INDEX `modified`
2025-06-02 11:33:10,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabContact Email` DROP INDEX `modified`
2025-06-02 11:33:10,777 WARNING database DDL Query made to DB:
ALTER TABLE `tabAssignment Rule Day` DROP INDEX `modified`
2025-06-02 11:33:10,806 WARNING database DDL Query made to DB:
ALTER TABLE `tabGlobal Search DocType` DROP INDEX `modified`
2025-06-02 11:33:10,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabBorder Processing Vehicle Details` DROP INDEX `modified`
2025-06-02 11:33:10,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoute Steps Table` DROP INDEX `modified`
2025-06-02 11:33:10,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepayment Schedule` DROP INDEX `modified`
2025-06-02 11:33:10,943 WARNING database DDL Query made to DB:
ALTER TABLE `tabAvailability Of Slots` DROP INDEX `modified`
2025-06-02 11:33:10,993 WARNING database DDL Query made to DB:
ALTER TABLE `tabEngine Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabBrake Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,085 WARNING database DDL Query made to DB:
ALTER TABLE `tabLighting Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,131 WARNING database DDL Query made to DB:
ALTER TABLE `tabBin List` DROP INDEX `modified`
2025-06-02 11:33:11,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Field` DROP INDEX `modified`
2025-06-02 11:33:11,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccounting Dimension Detail` DROP INDEX `modified`
2025-06-02 11:33:11,264 WARNING database DDL Query made to DB:
ALTER TABLE `tabRequested Funds Details` DROP INDEX `modified`
2025-06-02 11:33:11,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,360 WARNING database DDL Query made to DB:
ALTER TABLE `tabAir System Details` DROP INDEX `modified`
2025-06-02 11:33:11,403 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,445 WARNING database DDL Query made to DB:
ALTER TABLE `tabSuspension Details` DROP INDEX `modified`
2025-06-02 11:33:11,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,533 WARNING database DDL Query made to DB:
ALTER TABLE `tabTires Details` DROP INDEX `modified`
2025-06-02 11:33:11,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabSteering Details` DROP INDEX `modified`
2025-06-02 11:33:11,661 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,712 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectrical Details` DROP INDEX `modified`
2025-06-02 11:33:11,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Checklist` DROP INDEX `modified`
2025-06-02 11:33:11,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabElectronics Details` DROP INDEX `modified`
2025-06-02 11:33:11,851 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Details` DROP INDEX `modified`
2025-06-02 11:33:11,893 WARNING database DDL Query made to DB:
ALTER TABLE `tabPower Train Checklist` DROP INDEX `modified`
