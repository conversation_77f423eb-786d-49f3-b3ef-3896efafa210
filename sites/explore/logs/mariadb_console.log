_HiStOrY_V2_
USE\040explore;
ALTER\040TABLE\040`tabWorkstation`\040ADD\040COLUMN\040`indicator_color`\040VARCHAR(140)\040AFTER\040`description`;
DESCRIBE\040`tabSupplier\040Scorecard`;
ALTER\040TABLE\040`tabSupplier\040Scorecard`\040ADD\040COLUMN\040`indicator_color`\040VARCHAR(140);
ALTER\040TABLE\040`tabSupplier\040Scorecard`\040ADD\040COLUMN\040`indicator_color`\040varchar(140)\040DEFAULT\040NULL;
DESCRIBE\040`tabSupplier\040Scorecard`;
SELECT\040indicator_color\040FROM\040`tabSupplier\040Scorecard`\040LIMIT\0401;
SELECT\040COUNT(*)\040FROM\040`tabSupplier\040Scorecard`;
SELECT\040COUNT(*)\040FROM\040`tabSupplier\040Scorecard`\040WHERE\040indicator_color\040IS\040NOT\040NULL;
INSERT\040INTO\040`tabSupplier\040Scorecard`\040(name,\040indicator_color)\040VALUES\040('TestRecord',\040'Red');
SELECT\040indicator_color\040FROM\040`tabSupplier\040Scorecard`\040WHERE\040name\040=\040'TestRecord';
SELECT\040DISTINCT\040bl_no\040FROM\040`tabGate\040Pass`\040WHERE\040bl_no\040IS\040NOT\040NULL;
what\040are\040you\040doing
quit
exit
q
SELECT\040*\040FROM\040`tabGate\040Pass`\040WHERE\040name\040=\040'ICD-GP-2025-05552';
SELECT\040name,\040docstatus,\040container_id,\040transporter,\040truck,\040trailer,\040driver,\040license_no,\040is_empty_container,\040action_for_missing_booking\040FROM\040`tabGate\040Pass`\040WHERE\040name\040=\040'ICD-GP-2025-05552';
SELECT\040name,\040status,\040has_removal_charges,\040r_sales_invoice,\040has_corridor_levy_charges,\040c_sales_invoice,\040days_to_be_billed\040FROM\040`tabContainer`\040WHERE\040name\040=\040'ICD-C-2025-05664';
SELECT\040name,\040docstatus,\040has_stripping_charges,\040s_sales_invoice,\040has_custom_verification_charges,\040cv_sales_invoice\040FROM\040`tabIn\040Yard\040Container\040Booking`\040WHERE\040container_id\040=\040'ICD-C-2025-05664'\040AND\040docstatus\040!=\0402;
SELECT\040name,\040docstatus,\040has_reception_charges,\040re_sales_invoice\040FROM\040`tabReception`\040WHERE\040container_id\040=\040'ICD-C-2025-05664'\040AND\040docstatus\040!=\0402;
2;
SELECT\040name,\040docstatus,\040has_reception_charges,\040re_sales_invoice\040FROM\040`tabReception`\040WHERE\040container_id\040=\040'ICD-C-2025-05664'\040AND\040docstatus\040!=\0402;
2;
Empty\040set\040(0.001\040sec);
SELECT\040name,\040docstatus,\040has_inspection_charges,\040i_sales_invoice\040FROM\040`tabInspection`\040WHERE\040container_id\040=\040'ICD-C-2025-05664'\040AND\040docstatus\040!=\0402;
2;
Empty\040set\040(0.001\040sec);
exit;
