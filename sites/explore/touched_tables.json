["tabWork", "tabEmployee Grade", "tabEmployee Health Insurance", "tabTag Link", "tab<PERSON><PERSON><PERSON>", "tabEmployee Cost Center", "tabEmployee Feedback Rating", "tabCustom Field", "tabSalary", "tabOpportunity", "tabAsset Movement", "tabAppraisal", "tabLeave Block List", "tabSales Invoice Item", "tabPayroll", "tabSingles", "tabPatch", "tabPayroll Employee Detail", "tabJob <PERSON>", "tabLeave Policy Assignment", "tabOffer", "tabCompensatory", "tabLeave Type", "tabGratuity Rule Slab", "tabPortal", "tabDepartment", "tabLeave <PERSON>", "tabGoal", "tabLeave Policy", "tabCustom", "tabAppraisal Cycle", "tabEmployee Tax Exemption Proof Submission", "tabDelivery Note Item", "tabInterview Round", "tabTraining Result Employee", "tabLeave Application", "tabWorkspace Custom Block", "tabDeleted Document", "tabVersion", "tabSalary Structure Assignment", "tabEmployee Checkin", "tabEmployee Separation", "tabSales Order", "tabInterview Detail", "tabInstalled Application", "tabTraining Event Employee", "tabLeave Allocation", "tabExpected Skill Set", "tabShift Type", "tabEmployee Promotion", "tabEmployee Skill", "tabDaily", "tabEmployee Advance", "tabAppraisal KRA", "tabEmployment", "tabInterview Feedback", "tabAdditional Salary", "tabKRA", "tabStaffing", "tabLeave Policy Detail", "tabLeave Block List Date", "tabJ<PERSON>", "tabEmployee Training", "tabSales Invoice", "tabEmployee Separation Template", "tabPWA Notification", "tabStaffing Plan Detail", "tabIncome Tax Slab Other Charges", "tabComment", "tabEmployee Onboarding", "tabWorkspace Number Card", "tabAppointment", "tabCompensatory Leave Request", "tabTravel", "tabExit Interview", "tabEmployee Feedback Criteria", "tabEmployee Property History", "tabJob Applicant Source", "tabTraining Result", "tabPOS Invoice Merge Log", "tabLeave Period", "tabDesignation Skill", "tabVehicle Service", "tabSalary Component Account", "tabWorkspace Link", "tabExpense Claim", "tabGrievance", "tabLeave Block List Allow", "tabIncome", "tabScheduled Job Log", "tabAppraisee", "tabEmployee Benefit Application Detail", "tabProject", "tabPick List", "tabHas Role", "tabAppraisal Template Goal", "tabAttendance Request", "tabDocField", "tabPrint Heading", "tabEmployee Tax Exemption Proof Submission Detail", "tabAdditional", "tabExpense Claim Account", "tabEmployee OT Component", "tabEmployee Ben<PERSON><PERSON>", "tabExpense Claim Advance", "tabEmployee Incentive", "tabJob Requisition", "tabEmployee Performance Feedback", "tabGratuity Rule", "tabDocType Link", "tabAppraisal Template", "tabPWA", "tabVehicle Service Item", "tabGratuity", "tabTraining", "tabTraining Feedback", "tabWorkspace", "tabEmployee Other Income", "tabExpected", "tabEmployee Tax Exemption Declaration", "tabLeave", "tabRetention Bonus", "tabAsset Repair Consumed Item", "tabPayroll Entry", "tabPayroll Period Date", "tabGrievance Type", "tabFull", "tabAppointment Letter", "tabDocPerm", "tabAttendance", "tab<PERSON><PERSON><PERSON> Slip", "tabPurpose", "tabOffer Term", "tabEmployee Benefit Application", "tabSalary Component", "tabShift Assignment", "tabTaxable", "tabVehicle", "tabScheduled Job Type", "tabWorkspace Chart", "tabExpense", "tabEmployee Tax Exemption Category", "tabContainer", "tabDaily Work Summary Group User", "tabTraining Event", "tabMaterial", "tabHas", "tabSales", "tabPick", "tabLead", "tabSkill Assessment", "tabDepartment Approver", "tabCustomer", "tabAsset", "tabTraining Program", "tabGratuity Applicable Component", "tabExpense Claim <PERSON>ail", "tabSkill", "tabExit", "tabRetention", "tabEmployee Tax Exemption Sub Category", "tabInterview", "tabInterview Type", "tabShift", "tabAsset Repair", "tabDocType Action", "tabJob Offer Term Template", "tabInterviewer", "tabDeleted", "tabScheduled", "tabEmployee", "tabIdentification", "tabEmployment Type", "tabWork Order", "tabJob", "tabVehicle Log", "tabDelivery", "tabMode", "tabPortal Menu Item", "tabEmployee Onboarding Template", "tabEmployee Transfer", "tabIdentification Document Type", "tabPatch Log", "tabShift Request", "tabSalary Slip Timesheet", "tabStaffing Plan", "tabJob Applicant", "tabDocType", "tabEmployee Referral", "tab<PERSON><PERSON>", "tabWorkspace Quick List", "tabSalary Detail", "tabPOS", "tabEmployee Grievance", "tabL<PERSON><PERSON> Entry", "tabDesignation", "tabPrint", "tabDocType State", "tabDelivery Note", "tabPick List Item", "tabInterest", "tabSalary Slip OT Component", "tabEmployee Tax Exemption Declaration Category", "tabTag", "tabExpense Claim Type", "tabTaxable Salary Slab", "tabSalary Structure", "tabEmployee Skill Map", "tabMaterial Request", "tabProperty Setter", "tabTravel Itinerary", "tabAppointment Letter Template", "tabAppraisal Goal", "tabDaily Work Summary Group", "tabEmployee Boarding Activity", "tabWorkspace Shortcut", "tabInstalled", "tabProperty"]